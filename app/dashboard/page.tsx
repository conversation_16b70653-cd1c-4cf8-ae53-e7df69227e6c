"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, Truck, Briefcase, Users, Plus, Clock, MapPin, AlertTriangle, Loader2 } from "lucide-react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { EquipmentLocationModal } from "@/components/ui/equipment-location-modal"
import { useEquipment } from "@/lib/hooks/useEquipment"
import { useTeamMembers } from "@/lib/hooks/useTeamMembers"
import { useState, useMemo } from "react"

// This will be calculated from live equipment data

// Recent activity will be generated from live equipment data

export default function DashboardPage() {
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false)
  const { equipment, loading: equipmentLoading, error: equipmentError } = useEquipment({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000
  })

  const { teamMembers, loading: teamLoading, error: teamError } = useTeamMembers({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000
  })

  const loading = equipmentLoading || teamLoading
  const error = equipmentError || teamError

  // Calculate live KPI data from equipment
  const kpiData = useMemo(() => {
    if (!equipment.length) {
      return [
        {
          title: "Fleet Utilization",
          value: "Loading...",
          percentage: "...",
          trend: "...",
          icon: Truck,
          color: "text-blue-600",
          bgColor: "bg-blue-50",
        },
        {
          title: "Available Equipment",
          value: "Loading...",
          percentage: "...",
          trend: "...",
          icon: TrendingUp,
          color: "text-green-600",
          bgColor: "bg-green-50",
        },
        {
          title: "Active Jobs",
          value: "12 Jobs In Progress",
          percentage: "85%",
          trend: "+8%",
          icon: Briefcase,
          color: "text-yellow-600",
          bgColor: "bg-yellow-50",
        },
        {
          title: "Fleet Locations",
          value: "View Map",
          percentage: "Real-time",
          trend: "GPS tracking",
          icon: MapPin,
          color: "text-red-600",
          bgColor: "bg-red-50",
          clickable: true,
        },
      ]
    }

    const totalEquipment = equipment.length
    const availableEquipment = equipment.filter(eq => eq.status === 'available').length
    const inUseEquipment = equipment.filter(eq => eq.status === 'in_use').length
    const maintenanceEquipment = equipment.filter(eq =>
      eq.status === 'maintenance' || eq.status === 'repair'
    ).length

    const utilizationPercentage = totalEquipment > 0
      ? Math.round((inUseEquipment / totalEquipment) * 100)
      : 0

    const availabilityPercentage = totalEquipment > 0
      ? Math.round((availableEquipment / totalEquipment) * 100)
      : 0

    return [
      {
        title: "Fleet Utilization",
        value: `${inUseEquipment}/${totalEquipment} Units Active`,
        percentage: `${utilizationPercentage}%`,
        trend: utilizationPercentage >= 75 ? "+High" : utilizationPercentage >= 50 ? "Normal" : "-Low",
        icon: Truck,
        color: "text-blue-600",
        bgColor: "bg-blue-50",
      },
      {
        title: "Available Equipment",
        value: `${availableEquipment} Units Available`,
        percentage: `${availabilityPercentage}%`,
        trend: availableEquipment > 0 ? "Ready" : "None",
        icon: TrendingUp,
        color: "text-green-600",
        bgColor: "bg-green-50",
      },
      {
        title: "Team Members",
        value: `${teamMembers.filter(m => m.availability?.status === 'available').length}/${teamMembers.length} Available`,
        percentage: teamMembers.length > 0 ? `${Math.round((teamMembers.filter(m => m.availability?.status === 'available').length / teamMembers.length) * 100)}%` : "0%",
        trend: teamMembers.filter(m => m.availability?.status === 'available').length > 0 ? "Ready" : "None",
        icon: Users,
        color: "text-yellow-600",
        bgColor: "bg-yellow-50",
      },
      {
        title: "Fleet Locations",
        value: "View Map",
        percentage: "Real-time",
        trend: "GPS tracking",
        icon: MapPin,
        color: "text-red-600",
        bgColor: "bg-red-50",
        clickable: true,
      },
    ]
  }, [equipment, teamMembers])

  // Calculate live quick stats
  const quickStats = useMemo(() => {
    if (!equipment.length) {
      return {
        efficiency: "Loading...",
        fuelConsumption: "Loading...",
        maintenanceDue: "Loading...",
        jobsCompleted: "47 This Month" // This would come from jobs API
      }
    }

    const maintenanceDue = equipment.filter(eq =>
      eq.status === 'maintenance' || eq.status === 'repair'
    ).length

    const totalEquipment = equipment.length
    const activeEquipment = equipment.filter(eq => eq.status === 'in_use').length
    const efficiency = totalEquipment > 0
      ? Math.round((activeEquipment / totalEquipment) * 100)
      : 0

    // Mock fuel consumption based on active equipment
    const estimatedFuelConsumption = activeEquipment * 180 // ~180L per active unit per day

    return {
      efficiency: `${efficiency}%`,
      fuelConsumption: `${estimatedFuelConsumption.toLocaleString()}L`,
      maintenanceDue: `${maintenanceDue} Unit${maintenanceDue !== 1 ? 's' : ''}`,
      jobsCompleted: "47 This Month" // This would come from jobs API
    }
  }, [equipment])

  // Generate recent activity from live equipment and team data
  const recentActivity = useMemo(() => {
    if (!equipment.length && !teamMembers.length) {
      return [
        {
          id: 1,
          type: "equipment",
          message: "Loading activity...",
          time: "...",
          location: "...",
          status: "info",
        }
      ]
    }

    const activities = []

    // Add activities for equipment in maintenance/repair
    equipment.filter(eq => eq.status === 'maintenance' || eq.status === 'repair').forEach((eq, index) => {
      activities.push({
        id: `maintenance-${eq.id}`,
        type: "equipment",
        message: `${eq.name} ${eq.status === 'repair' ? 'requires repair' : 'in maintenance'}`,
        time: `${Math.floor(Math.random() * 12) + 1} hours ago`,
        location: eq.currentLocation?.address || 'Shop',
        status: eq.status === 'repair' ? 'warning' : 'info',
      })
    })

    // Add activities for equipment in use
    equipment.filter(eq => eq.status === 'in_use').slice(0, 2).forEach((eq, index) => {
      activities.push({
        id: `active-${eq.id}`,
        type: "job",
        message: `${eq.name} assigned to active job`,
        time: `${Math.floor(Math.random() * 8) + 1} hours ago`,
        location: eq.currentLocation?.address || 'Job Site',
        status: "active",
      })
    })

    // Add activities for team members
    teamMembers.filter(m => m.availability?.status === 'assigned' || m.availability?.status === 'busy').slice(0, 2).forEach((member, index) => {
      activities.push({
        id: `team-${member.id}`,
        type: "team",
        message: `${member.firstName} ${member.lastName} ${member.availability?.status === 'assigned' ? 'assigned to job' : 'currently working'}`,
        time: `${Math.floor(Math.random() * 6) + 1} hours ago`,
        location: member.availability?.currentLocation?.name || 'Job Site',
        status: "active",
      })
    })

    return activities.slice(0, 4) // Limit to 4 activities
  }, [equipment, teamMembers])

  const handleLocationClick = () => {
    setIsLocationModalOpen(true)
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <SidebarTrigger className="text-gray-600 hover:bg-gray-100" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Welcome back, John. Here's what's happening with your fleet today.</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Job
          </Button>
          <Button variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            Add Equipment
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {loading ? (
          // Loading state
          Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
                </CardTitle>
                <div className="p-2 rounded-lg bg-gray-100">
                  <Loader2 className="h-4 w-4 text-gray-400 animate-spin" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="flex items-center gap-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-12"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : error ? (
          // Error state
          <Card className="col-span-full">
            <CardContent className="py-8">
              <div className="text-center text-red-600">
                Error loading equipment data: {error}
              </div>
            </CardContent>
          </Card>
        ) : (
          // Live data
          kpiData.map((kpi) => (
            <Card
              key={kpi.title}
              className={`hover:shadow-md transition-shadow ${
                kpi.clickable ? 'cursor-pointer hover:shadow-lg' : ''
              }`}
              onClick={kpi.clickable ? handleLocationClick : undefined}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">{kpi.title}</CardTitle>
                <div className={`p-2 rounded-lg ${kpi.bgColor}`}>
                  <kpi.icon className={`h-4 w-4 ${kpi.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{kpi.value}</div>
                <div className="flex items-center gap-2 mt-1">
                  <span className={`text-sm font-medium ${kpi.color}`}>({kpi.percentage})</span>
                  <Badge variant="secondary" className="text-xs">
                    {kpi.trend}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50">
                  <div
                    className={`p-2 rounded-full ${
                      activity.status === "completed"
                        ? "bg-green-100"
                        : activity.status === "warning"
                          ? "bg-yellow-100"
                          : activity.status === "active"
                            ? "bg-blue-100"
                            : "bg-gray-100"
                    }`}
                  >
                    {activity.type === "equipment" && <Truck className="h-4 w-4 text-green-600" />}
                    {activity.type === "job" && <Briefcase className="h-4 w-4 text-blue-600" />}
                    {activity.type === "alert" && <AlertTriangle className="h-4 w-4 text-yellow-600" />}
                    {activity.type === "team" && <Users className="h-4 w-4 text-gray-600" />}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                    <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                      <span>{activity.time}</span>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {activity.location}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Stats</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {loading ? (
              // Loading state for quick stats
              Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="flex justify-between items-center">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
                </div>
              ))
            ) : (
              // Live quick stats
              <>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Equipment Efficiency</span>
                  <span className="text-sm font-semibold text-green-600">{quickStats.efficiency}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Fuel Consumption</span>
                  <span className="text-sm font-semibold text-blue-600">{quickStats.fuelConsumption}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Maintenance Due</span>
                  <span className={`text-sm font-semibold ${
                    quickStats.maintenanceDue.startsWith('0') ? 'text-green-600' : 'text-yellow-600'
                  }`}>
                    {quickStats.maintenanceDue}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Active Team Members</span>
                  <span className="text-sm font-semibold text-purple-600">
                    {teamMembers.filter(m => m.availability?.status === 'available' || m.availability?.status === 'assigned' || m.availability?.status === 'busy').length}
                  </span>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Location Modal */}
      <EquipmentLocationModal
        isOpen={isLocationModalOpen}
        onClose={() => setIsLocationModalOpen(false)}
        equipment={equipment}
      />
    </div>
  )
}
