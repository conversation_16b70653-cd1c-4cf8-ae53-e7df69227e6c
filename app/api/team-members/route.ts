import { NextRequest, NextResponse } from 'next/server'
import { getFirebaseAdmin } from '@/lib/firebase/admin'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'
import { TeamMember } from '@/lib/types/firestore'

// GET /api/team-members - List all team members for an organization
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const orgId = searchParams.get('orgId') || 'demo-org'
    const status = searchParams.get('status') // Filter by status
    const department = searchParams.get('department') // Filter by department
    
    const app = getFirebaseAdmin()
    const db = getFirestore(app)

    console.log(`👥 Fetching team members for organization: ${orgId}`)

    let query = db
      .collection('organizations')
      .doc(orgId)
      .collection('teamMembers')
      .orderBy('createdAt', 'desc')

    // Apply filters if provided
    if (status) {
      query = query.where('status', '==', status)
    }
    if (department) {
      query = query.where('department', '==', department)
    }

    const teamMembersSnapshot = await query.get()

    const teamMembers = teamMembersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))

    return NextResponse.json({
      success: true,
      data: teamMembers,
      count: teamMembers.length,
      organizationId: orgId,
      filters: { status, department },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error fetching team members:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch team members',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// POST /api/team-members - Create a new team member
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { orgId = 'demo-org', ...teamMemberData } = body
    
    const app = getFirebaseAdmin()
    const db = getFirestore(app)

    console.log(`👥 Creating new team member for organization: ${orgId}`)

    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'position', 'department']
    for (const field of requiredFields) {
      if (!teamMemberData[field]) {
        return NextResponse.json({
          success: false,
          error: `Missing required field: ${field}`,
          timestamp: new Date().toISOString()
        }, { status: 400 })
      }
    }

    const now = Timestamp.now()
    const newTeamMember = {
      ...teamMemberData,
      organizationId: orgId,
      status: teamMemberData.status || 'active',
      hireDate: teamMemberData.hireDate ? Timestamp.fromDate(new Date(teamMemberData.hireDate)) : now,
      availability: {
        status: 'available',
        lastUpdated: now,
        ...teamMemberData.availability
      },
      skills: teamMemberData.skills || {},
      createdAt: now,
      updatedAt: now,
      createdBy: 'api'
    }

    const docRef = await db
      .collection('organizations')
      .doc(orgId)
      .collection('teamMembers')
      .add(newTeamMember)

    const createdTeamMember = {
      id: docRef.id,
      ...newTeamMember
    }

    console.log(`✅ Team member created successfully: ${docRef.id}`)

    return NextResponse.json({
      success: true,
      data: createdTeamMember,
      message: 'Team member created successfully',
      timestamp: new Date().toISOString()
    }, { status: 201 })

  } catch (error) {
    console.error('❌ Error creating team member:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create team member',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
