import { NextRequest, NextResponse } from 'next/server'
import { getFirebaseAdmin } from '@/lib/firebase/admin'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'

// GET /api/team-members/[id] - Get specific team member
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const orgId = searchParams.get('orgId') || 'demo-org'
    const memberId = params.id
    
    const app = getFirebaseAdmin()
    const db = getFirestore(app)

    console.log(`👥 Fetching team member ${memberId} for organization: ${orgId}`)

    const memberDoc = await db
      .collection('organizations')
      .doc(orgId)
      .collection('teamMembers')
      .doc(memberId)
      .get()

    if (!memberDoc.exists) {
      return NextResponse.json({
        success: false,
        error: 'Team member not found',
        timestamp: new Date().toISOString()
      }, { status: 404 })
    }

    const teamMember = {
      id: memberDoc.id,
      ...memberDoc.data()
    }

    return NextResponse.json({
      success: true,
      data: teamMember,
      organizationId: orgId,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error fetching team member:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch team member',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// PATCH /api/team-members/[id] - Update specific team member
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { orgId = 'demo-org', ...updateData } = body
    const memberId = params.id
    
    const app = getFirebaseAdmin()
    const db = getFirestore(app)

    console.log(`🔧 Updating team member ${memberId} for organization: ${orgId}`)

    // Remove undefined values and add updatedAt timestamp
    const cleanUpdateData = Object.fromEntries(
      Object.entries(updateData).filter(([_, value]) => value !== undefined)
    )

    // Handle date fields
    if (cleanUpdateData.hireDate) {
      cleanUpdateData.hireDate = Timestamp.fromDate(new Date(cleanUpdateData.hireDate))
    }

    // Update availability timestamp if availability is being updated
    if (cleanUpdateData.availability) {
      cleanUpdateData.availability = {
        ...cleanUpdateData.availability,
        lastUpdated: Timestamp.now()
      }
    }

    const updatePayload = {
      ...cleanUpdateData,
      updatedAt: Timestamp.now()
    }

    const memberRef = db
      .collection('organizations')
      .doc(orgId)
      .collection('teamMembers')
      .doc(memberId)

    // Check if member exists
    const memberDoc = await memberRef.get()
    if (!memberDoc.exists) {
      return NextResponse.json({
        success: false,
        error: 'Team member not found',
        timestamp: new Date().toISOString()
      }, { status: 404 })
    }

    await memberRef.update(updatePayload)

    // Fetch updated member
    const updatedDoc = await memberRef.get()
    const updatedMember = {
      id: updatedDoc.id,
      ...updatedDoc.data()
    }

    console.log(`✅ Team member updated successfully: ${memberId}`)

    return NextResponse.json({
      success: true,
      data: updatedMember,
      message: 'Team member updated successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error updating team member:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update team member',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// DELETE /api/team-members/[id] - Delete specific team member
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const orgId = searchParams.get('orgId') || 'demo-org'
    const memberId = params.id
    
    const app = getFirebaseAdmin()
    const db = getFirestore(app)

    console.log(`🗑️ Deleting team member ${memberId} for organization: ${orgId}`)

    const memberRef = db
      .collection('organizations')
      .doc(orgId)
      .collection('teamMembers')
      .doc(memberId)

    // Check if member exists
    const memberDoc = await memberRef.get()
    if (!memberDoc.exists) {
      return NextResponse.json({
        success: false,
        error: 'Team member not found',
        timestamp: new Date().toISOString()
      }, { status: 404 })
    }

    // Soft delete by updating status to 'terminated'
    await memberRef.update({
      status: 'terminated',
      availability: {
        status: 'unavailable',
        lastUpdated: Timestamp.now()
      },
      updatedAt: Timestamp.now(),
      terminatedAt: Timestamp.now()
    })

    console.log(`✅ Team member soft deleted successfully: ${memberId}`)

    return NextResponse.json({
      success: true,
      message: 'Team member deleted successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error deleting team member:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete team member',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
