"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Search, MoreHorizontal, MapPin, Clock, Wrench, Eye, RefreshCw, AlertCircle, Edit, Trash2, Calendar } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu"
import { useEquipment } from "@/lib/hooks/useEquipment"
import { transformEquipmentForUI, filterEquipment, getEquipmentStats, getStatusColor, getStatusDot, getStatusDisplayText, equipmentStatusOptions } from "@/lib/utils/equipment"
import { EquipmentModal } from "@/components/fleet/EquipmentModal"
import { EquipmentDetailsModal } from "@/components/fleet/EquipmentDetailsModal"
import { DeleteConfirmDialog } from "@/components/fleet/DeleteConfirmDialog"
import { EquipmentLocationModal } from "@/components/ui/equipment-location-modal"
import { Equipment } from "@/lib/types/firestore"
import { useToast } from "@/hooks/use-toast"

// Component now uses real Firestore data via useEquipment hook

export default function FleetOverviewPage() {
  const [statusFilter, setStatusFilter] = useState("all")
  const [sourceFilter, setSourceFilter] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")

  // Modal states
  const [isEquipmentModalOpen, setIsEquipmentModalOpen] = useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isLocationModalOpen, setIsLocationModalOpen] = useState(false)
  const [selectedEquipment, setSelectedEquipment] = useState<Equipment | null>(null)
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create')

  const { toast } = useToast()

  // Use the custom hook to fetch equipment data
  const { equipment: rawEquipment, loading, error, refetch, createEquipment, updateEquipment, deleteEquipment } = useEquipment({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000 // Refresh every 30 seconds
  })

  // Transform Firestore data for UI display
  const equipmentData = rawEquipment.map(transformEquipmentForUI)

  // Apply filters
  const filteredEquipment = filterEquipment(equipmentData, {
    status: statusFilter === "all" ? undefined : statusFilter,
    source: sourceFilter === "all" ? undefined : sourceFilter,
    search: searchTerm || undefined
  })

  // Get equipment statistics
  const stats = getEquipmentStats(equipmentData)

  // Get status counts for filter dropdown
  const getStatusCounts = () => {
    const counts: Record<string, number> = {}
    equipmentData.forEach(equipment => {
      counts[equipment.status] = (counts[equipment.status] || 0) + 1
    })
    return counts
  }

  const statusCounts = getStatusCounts()

  // CRUD handlers
  const handleAddEquipment = () => {
    setSelectedEquipment(null)
    setModalMode('create')
    setIsEquipmentModalOpen(true)
  }

  const handleEditEquipment = (equipment: Equipment) => {
    setSelectedEquipment(equipment)
    setModalMode('edit')
    setIsEquipmentModalOpen(true)
  }

  const handleViewDetails = (equipment: Equipment) => {
    setSelectedEquipment(equipment)
    setIsDetailsModalOpen(true)
  }

  const handleDeleteEquipment = (equipment: Equipment) => {
    setSelectedEquipment(equipment)
    setIsDeleteDialogOpen(true)
  }

  const handleSaveEquipment = async (data: Partial<Equipment>) => {
    try {
      if (modalMode === 'create') {
        await createEquipment(data)
        toast({
          title: "Equipment Added",
          description: `${data.name} has been successfully added to your fleet.`,
        })
      } else if (selectedEquipment) {
        await updateEquipment(selectedEquipment.id, data)
        toast({
          title: "Equipment Updated",
          description: `${data.name} has been successfully updated.`,
        })
      }
      setIsEquipmentModalOpen(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save equipment. Please try again.",
        variant: "destructive",
      })
      throw error
    }
  }

  const handleConfirmDelete = async () => {
    if (!selectedEquipment) return

    try {
      await deleteEquipment(selectedEquipment.id)
      toast({
        title: "Equipment Deleted",
        description: `${selectedEquipment.name} has been removed from your fleet.`,
      })
      setIsDeleteDialogOpen(false)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete equipment. Please try again.",
        variant: "destructive",
      })
      throw error
    }
  }

  const handleScheduleMaintenance = (equipment: Equipment) => {
    // TODO: Implement maintenance scheduling
    toast({
      title: "Feature Coming Soon",
      description: "Maintenance scheduling will be available in a future update.",
    })
  }

  const handleTrackLocation = (equipment: Equipment) => {
    setSelectedEquipment(equipment)
    setIsLocationModalOpen(true)
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Fleet Overview</h1>
          <p className="text-gray-600">
            Manage and monitor your equipment fleet
            {loading && <span className="text-blue-600 ml-2">• Loading...</span>}
            {!loading && <span className="text-green-600 ml-2">• Live from Firestore</span>}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={refetch} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleAddEquipment}>
            <Plus className="h-4 w-4 mr-2" />
            Add Equipment
          </Button>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <span>Error loading equipment: {error}</span>
              <Button variant="outline" size="sm" onClick={refetch} className="ml-auto">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search equipment..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status ({equipmentData.length})</SelectItem>
                {equipmentStatusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${getStatusDot(option.value)}`}></div>
                      <span>{option.label} ({statusCounts[option.value] || 0})</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={sourceFilter} onValueChange={setSourceFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sources</SelectItem>
                <SelectItem value="samsara">Samsara</SelectItem>
                <SelectItem value="manual">Manual</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Equipment Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Equipment ({filteredEquipment.length})</span>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-green-400"></div>
                Active ({stats.active})
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-yellow-400"></div>
                Maintenance ({stats.maintenance})
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-red-400"></div>
                Inactive ({stats.inactive})
              </div>
              <div className="text-xs text-gray-400 ml-2">
                Utilization: {stats.utilization}%
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin text-gray-400 mr-2" />
              <span className="text-gray-500">Loading equipment...</span>
            </div>
          ) : filteredEquipment.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">
                {equipmentData.length === 0
                  ? "No equipment found. Try adding some equipment or seeding demo data."
                  : "No equipment matches your current filters."
                }
              </p>
              {equipmentData.length === 0 && (
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => window.open('/firestore-demo', '_blank')}
                >
                  Seed Demo Data
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Equipment Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Operator</TableHead>
                  <TableHead>Last Update</TableHead>
                  <TableHead>Source</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEquipment.map((equipment) => (
                  <TableRow key={equipment.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">
                      <div>
                        <div>{equipment.name}</div>
                        {equipment.make && equipment.model && (
                          <div className="text-xs text-gray-500">
                            {equipment.make} {equipment.model} {equipment.year}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{equipment.type}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${getStatusDot(equipment.status)}`}></div>
                        <Badge variant="secondary" className={getStatusColor(equipment.status)}>
                          {getStatusDisplayText(equipment.status)}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3 text-gray-400" />
                        {equipment.location}
                      </div>
                    </TableCell>
                    <TableCell>{equipment.operator}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3 text-gray-400" />
                        {equipment.lastUpdate}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{equipment.source}</Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewDetails(rawEquipment.find(e => e.id === equipment.id)!)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditEquipment(rawEquipment.find(e => e.id === equipment.id)!)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Equipment
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleScheduleMaintenance(rawEquipment.find(e => e.id === equipment.id)!)}>
                            <Calendar className="h-4 w-4 mr-2" />
                            Schedule Maintenance
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleTrackLocation(rawEquipment.find(e => e.id === equipment.id)!)}>
                            <MapPin className="h-4 w-4 mr-2" />
                            Track Location
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDeleteEquipment(rawEquipment.find(e => e.id === equipment.id)!)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Equipment
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Modals */}
      <EquipmentModal
        isOpen={isEquipmentModalOpen}
        onClose={() => setIsEquipmentModalOpen(false)}
        onSave={handleSaveEquipment}
        equipment={selectedEquipment}
        mode={modalMode}
      />

      <EquipmentDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
        equipment={selectedEquipment}
        onEdit={handleEditEquipment}
        onDelete={handleDeleteEquipment}
      />

      <DeleteConfirmDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        equipment={selectedEquipment}
      />

      <EquipmentLocationModal
        isOpen={isLocationModalOpen}
        onClose={() => setIsLocationModalOpen(false)}
        equipment={rawEquipment}
        selectedEquipment={selectedEquipment}
      />
    </div>
  )
}
