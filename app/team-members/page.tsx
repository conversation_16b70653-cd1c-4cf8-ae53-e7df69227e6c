"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, Grid3X3, List, Phone, Mail, MapPin, Eye, Plus, Edit, Trash2, Loader2 } from "lucide-react"
import Link from "next/link"
import { useTeamMembers } from "@/lib/hooks/useTeamMembers"
import { TeamMemberFormModal } from "@/components/ui/team-member-form-modal"
import { TeamMember } from "@/lib/types/firestore"

// Helper functions for transforming data
const getFullName = (member: TeamMember) => `${member.firstName} ${member.lastName}`

const getSkillsArray = (member: TeamMember) => {
  return Object.keys(member.skills || {}).slice(0, 3)
}

const getSkillColor = (index: number) => {
  const colors = ["bg-blue-100 text-blue-800", "bg-green-100 text-green-800", "bg-purple-100 text-purple-800", "bg-red-100 text-red-800"]
  return colors[index % colors.length]
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'available':
      return 'ring-green-200'
    case 'assigned':
    case 'busy':
      return 'ring-yellow-200'
    case 'off-duty':
      return 'ring-gray-200'
    case 'unavailable':
      return 'ring-red-200'
    default:
      return 'ring-gray-200'
  }
}

const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (status) {
    case 'available':
      return 'default'
    case 'assigned':
    case 'busy':
      return 'secondary'
    case 'off-duty':
      return 'outline'
    case 'unavailable':
      return 'destructive'
    default:
      return 'outline'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'available':
      return 'Available'
    case 'assigned':
      return 'Assigned'
    case 'busy':
      return 'Busy'
    case 'off-duty':
      return 'Off Duty'
    case 'unavailable':
      return 'Unavailable'
    default:
      return 'Unknown'
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "available":
      return "ring-green-400 bg-green-50"
    case "busy":
      return "ring-yellow-400 bg-yellow-50"
    case "off-duty":
      return "ring-red-400 bg-red-50"
    default:
      return "ring-gray-400 bg-gray-50"
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case "available":
      return "Available"
    case "busy":
      return "Busy on Job Site"
    case "off-duty":
      return "Off Duty"
    default:
      return "Unknown"
  }
}

const getSkillColor = (color: string) => {
  switch (color) {
    case "blue":
      return "bg-blue-100 text-blue-800"
    case "red":
      return "bg-red-100 text-red-800"
    case "green":
      return "bg-green-100 text-green-800"
    case "purple":
      return "bg-purple-100 text-purple-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

export default function TeamMembersPage() {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [statusFilter, setStatusFilter] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [isFormModalOpen, setIsFormModalOpen] = useState(false)
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Fetch team members data
  const { teamMembers, loading, error, refetch, createTeamMember, updateTeamMember, deleteTeamMember } = useTeamMembers({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000
  })

  const filteredMembers = teamMembers.filter((member) => {
    const matchesStatus = statusFilter === "all" || member.availability?.status === statusFilter
    const matchesSearch =
      getFullName(member).toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
      Object.keys(member.skills || {}).some((skill) => skill.toLowerCase().includes(searchTerm.toLowerCase()))

    return matchesStatus && matchesSearch
  })

  const handleCreateMember = () => {
    setEditingMember(null)
    setIsFormModalOpen(true)
  }

  const handleEditMember = (member: TeamMember) => {
    setEditingMember(member)
    setIsFormModalOpen(true)
  }

  const handleDeleteMember = async (member: TeamMember) => {
    if (confirm(`Are you sure you want to delete ${getFullName(member)}?`)) {
      await deleteTeamMember(member.id)
    }
  }

  const handleFormSubmit = async (data: Partial<TeamMember>) => {
    setIsSubmitting(true)
    try {
      if (editingMember) {
        await updateTeamMember(editingMember.id, data)
      } else {
        await createTeamMember(data)
      }
      setIsFormModalOpen(false)
      setEditingMember(null)
    } catch (error) {
      console.error('Error saving team member:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading team members...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="text-center text-red-600">
          <p>Error loading team members: {error}</p>
          <Button onClick={refetch} className="mt-2">Retry</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Team Members</h1>
          <p className="text-gray-600">{teamMembers.length} Active Members</p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleCreateMember}>
            <Plus className="h-4 w-4 mr-2" />
            Add Member
          </Button>
          <Button variant={viewMode === "grid" ? "default" : "outline"} size="sm" onClick={() => setViewMode("grid")}>
            <Grid3X3 className="h-4 w-4 mr-2" />
            Grid View
          </Button>
          <Button variant={viewMode === "list" ? "default" : "outline"} size="sm" onClick={() => setViewMode("list")}>
            <List className="h-4 w-4 mr-2" />
            List View
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by name, role, or skill..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Members</SelectItem>
                <SelectItem value="available">Available</SelectItem>
                <SelectItem value="assigned">Assigned</SelectItem>
                <SelectItem value="busy">Busy</SelectItem>
                <SelectItem value="off-duty">Off Duty</SelectItem>
                <SelectItem value="unavailable">Unavailable</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Team Members Grid */}
      {viewMode === "grid" && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredMembers.map((member) => (
            <Card key={member.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="relative">
                    <Avatar className={`h-16 w-16 ring-4 ${getStatusColor(member.availability?.status || 'available')}`}>
                      <AvatarImage src={member.profile?.avatar || "/placeholder.svg"} />
                      <AvatarFallback>
                        {getFullName(member)
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                  </div>

                  <div>
                    <h3 className="font-semibold text-gray-900">{getFullName(member)}</h3>
                    <p className="text-sm text-gray-600">{member.position}</p>
                  </div>

                  <div className="flex flex-wrap gap-1 justify-center">
                    {getSkillsArray(member).map((skill, index) => (
                      <Badge
                        key={skill}
                        variant="secondary"
                        className={`text-xs ${getSkillColor(index)}`}
                      >
                        {skill.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Badge>
                    ))}
                  </div>

                  <div className="w-full space-y-2">
                    <p className="text-sm font-medium text-gray-900">{getStatusText(member.availability?.status || 'available')}</p>
                    <div className="flex items-center justify-center gap-1 text-xs text-gray-500">
                      <MapPin className="h-3 w-3" />
                      {member.availability?.currentLocation?.name || 'Not specified'}
                    </div>
                  </div>

                  <div className="flex gap-1 w-full">
                    <Button variant="outline" size="sm" onClick={() => handleEditMember(member)} className="flex-1">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleDeleteMember(member)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Team Members List */}
      {viewMode === "list" && (
        <Card>
          <CardContent className="p-0">
            <div className="divide-y divide-gray-200">
              {filteredMembers.map((member) => (
                <div key={member.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <Avatar className={`h-12 w-12 ring-2 ${getStatusColor(member.availability?.status || 'available')}`}>
                        <AvatarImage src={member.profile?.avatar || "/placeholder.svg"} />
                        <AvatarFallback>
                          {getFullName(member)
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>

                      <div>
                        <h3 className="font-semibold text-gray-900">{getFullName(member)}</h3>
                        <p className="text-sm text-gray-600">{member.position}</p>
                        <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                          {member.phone && (
                            <div className="flex items-center gap-1">
                              <Phone className="h-3 w-3" />
                              {member.phone}
                            </div>
                          )}
                          {member.email && (
                            <div className="flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              {member.email}
                            </div>
                          )}
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {member.availability?.currentLocation?.name || 'Not specified'}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="flex flex-wrap gap-1">
                        {getSkillsArray(member).map((skill, index) => (
                          <Badge
                            key={skill}
                            variant="secondary"
                            className={`text-xs ${getSkillColor(index)}`}
                          >
                            {skill.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </Badge>
                        ))}
                      </div>

                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">{getStatusText(member.availability?.status || 'available')}</p>
                        <div className="flex gap-1 mt-2">
                          <Button variant="outline" size="sm" onClick={() => handleEditMember(member)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleDeleteMember(member)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Team Member Form Modal */}
      <TeamMemberFormModal
        isOpen={isFormModalOpen}
        onClose={() => {
          setIsFormModalOpen(false)
          setEditingMember(null)
        }}
        onSubmit={handleFormSubmit}
        teamMember={editingMember}
        isLoading={isSubmitting}
      />
    </div>
  )
}
