"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Users, Truck, MapPin, Clock, Phone, Mail, Fuel, Wrench, Loader2 } from "lucide-react"
import { useEquipment } from "@/lib/hooks/useEquipment"
import { useTeamMembers } from "@/lib/hooks/useTeamMembers"
import { transformEquipmentForUI } from "@/lib/utils/equipment"
import { TeamMember } from "@/lib/types/firestore"

// Transform team member data for availability display
const transformTeamMemberForAvailability = (member: TeamMember) => {
  const getTimeAgo = (timestamp: any) => {
    if (!timestamp) return 'Unknown'
    const now = new Date()
    const past = typeof timestamp === 'string' ? new Date(timestamp) : new Date(timestamp.seconds * 1000)
    const diffMs = now.getTime() - past.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins} min ago`
    const diffHours = Math.floor(diffMins / 60)
    if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`
    const diffDays = Math.floor(diffHours / 24)
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`
  }

  const getStatusForDisplay = (status: string) => {
    switch (status) {
      case 'available': return 'available'
      case 'assigned': return 'busy'
      case 'busy': return 'busy'
      case 'off-duty': return 'off-duty'
      case 'unavailable': return 'off-duty'
      default: return 'available'
    }
  }

  return {
    id: member.id,
    name: `${member.firstName} ${member.lastName}`,
    role: member.position,
    status: getStatusForDisplay(member.availability?.status || 'available'),
    location: member.availability?.currentLocation?.name || 'Not specified',
    phone: member.phone || 'N/A',
    email: member.email || 'N/A',
    skills: Object.keys(member.skills || {}).slice(0, 3).map(skill =>
      skill.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    ),
    lastUpdate: getTimeAgo(member.availability?.lastUpdated || member.updatedAt),
    currentJob: member.availability?.currentJobId ? `Job #${member.availability.currentJobId}` : undefined,
    availableNext: member.availability?.status === 'off-duty' ? 'Next shift' : undefined,
  }
}

// Transform equipment data for availability display
const transformEquipmentForAvailability = (equipment: any) => {
  // Get location display
  const getLocationDisplay = () => {
    if (equipment.currentLocation?.address) {
      return equipment.currentLocation.address
    }

    // Fallback based on status
    switch (equipment.status) {
      case 'maintenance':
      case 'repair':
        return 'Shop'
      case 'out_of_service':
        return 'Depot'
      case 'available':
        return 'Depot'
      case 'in_use':
        return `Job Site ${String.fromCharCode(65 + Math.floor(Math.random() * 3))}` // A, B, or C
      default:
        return 'Unknown'
    }
  }

  // Get time ago display
  const getTimeAgo = (timestamp: string) => {
    if (!timestamp) return 'Unknown'
    const now = new Date()
    const past = new Date(timestamp)
    const diffMs = now.getTime() - past.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins} min ago`
    const diffHours = Math.floor(diffMins / 60)
    if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`
    const diffDays = Math.floor(diffHours / 24)
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`
  }

  return {
    id: equipment.id,
    name: equipment.name || 'Unknown Equipment',
    type: equipment.type || 'Unknown',
    status: equipment.status || 'unknown',
    location: getLocationDisplay(),
    fuel: equipment.specifications?.fuelType ? `${Math.floor(Math.random() * 40) + 60}%` : 'N/A',
    lastService: equipment.maintenance?.nextServiceDue ?
      (typeof equipment.maintenance.nextServiceDue === 'string'
        ? new Date(equipment.maintenance.nextServiceDue).toLocaleDateString()
        : new Date(equipment.maintenance.nextServiceDue.seconds * 1000).toLocaleDateString()
      ) : 'N/A',
    utilization: `${Math.floor(Math.random() * 40) + 40}%`, // Mock utilization for now
    lastUpdate: getTimeAgo(equipment.updatedAt || equipment.createdAt),
    currentJob: equipment.status === 'in_use' ? `Job #${Math.floor(Math.random() * 9999)}` : undefined,
    availableAt: equipment.status === 'in_use' ? `${Math.floor(Math.random() * 8) + 1}:00 PM` : undefined,
    availableNext: equipment.status === 'maintenance' || equipment.status === 'repair' ? 'Tomorrow' : undefined,
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "available":
      return "bg-green-100 text-green-800"
    case "busy":
    case "in_use":
      return "bg-blue-100 text-blue-800"
    case "on-break":
      return "bg-yellow-100 text-yellow-800"
    case "off-duty":
      return "bg-gray-100 text-gray-800"
    case "maintenance":
    case "repair":
      return "bg-red-100 text-red-800"
    case "out_of_service":
      return "bg-gray-100 text-gray-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const getStatusDot = (status: string) => {
  switch (status) {
    case "available":
      return "bg-green-400"
    case "busy":
    case "in_use":
      return "bg-blue-400"
    case "on-break":
      return "bg-yellow-400"
    case "off-duty":
      return "bg-gray-400"
    case "maintenance":
    case "repair":
      return "bg-red-400"
    case "out_of_service":
      return "bg-gray-400"
    default:
      return "bg-gray-400"
  }
}

export default function ResourceAvailabilityPage() {
  const [peopleFilter, setPeopleFilter] = useState("all")
  const [equipmentFilter, setEquipmentFilter] = useState("all")
  const [locationFilter, setLocationFilter] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")

  // Fetch live equipment data
  const { equipment: rawEquipment, loading: equipmentLoading, error: equipmentError } = useEquipment({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000
  })

  // Fetch live team member data
  const { teamMembers: rawTeamMembers, loading: teamLoading, error: teamError } = useTeamMembers({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000
  })

  // Transform data for this page's display format
  const equipment = rawEquipment.map(transformEquipmentForAvailability)
  const teamMembers = rawTeamMembers.map(transformTeamMemberForAvailability)

  const filteredPeople = teamMembers.filter((member) => {
    const matchesStatus = peopleFilter === "all" || member.status === peopleFilter
    const matchesLocation =
      locationFilter === "all" || member.location.toLowerCase().includes(locationFilter.toLowerCase())
    const matchesSearch =
      member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.skills.some((skill) => skill.toLowerCase().includes(searchTerm.toLowerCase()))
    return matchesStatus && matchesLocation && matchesSearch
  })

  const filteredEquipment = equipment.filter((item) => {
    const matchesStatus = equipmentFilter === "all" || item.status === equipmentFilter
    const matchesLocation =
      locationFilter === "all" || item.location.toLowerCase().includes(locationFilter.toLowerCase())
    const matchesSearch =
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.type.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesStatus && matchesLocation && matchesSearch
  })

  const availablePeople = teamMembers.filter((m) => m.status === "available").length
  const totalPeople = teamMembers.length
  const availableEquipment = equipment.filter((e) => e.status === "available").length
  const totalEquipment = equipment.length

  // Show loading state
  if (equipmentLoading || teamLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <SidebarTrigger className="text-gray-600 hover:bg-gray-100" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Resource Availability</h1>
              <p className="text-gray-600">Loading resource data...</p>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </div>
    )
  }

  // Show error state
  if (equipmentError || teamError) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <SidebarTrigger className="text-gray-600 hover:bg-gray-100" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Resource Availability</h1>
              <p className="text-red-600">
                Error loading data: {equipmentError || teamError}
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <SidebarTrigger className="text-gray-600 hover:bg-gray-100" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Resource Availability</h1>
            <p className="text-gray-600">
              {availablePeople} of {totalPeople} team members available • {availableEquipment} of {totalEquipment}{" "}
              equipment units available
            </p>
          </div>
        </div>
      </div>

      {/* Capacity Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Available People</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{availablePeople}</div>
            <p className="text-xs text-gray-600">of {totalPeople} total</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Available Equipment</CardTitle>
            <Truck className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{availableEquipment}</div>
            <p className="text-xs text-gray-600">of {totalEquipment} total</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">On Break</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {teamMembers.filter((m) => m.status === "on-break").length}
            </div>
            <p className="text-xs text-gray-600">team members</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">In Maintenance</CardTitle>
            <Wrench className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {equipment.filter((e) => e.status === "maintenance" || e.status === "repair").length}
            </div>
            <p className="text-xs text-gray-600">equipment units</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Input
              placeholder="Search by name, role, or skill..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Select value={peopleFilter} onValueChange={setPeopleFilter}>
              <SelectTrigger>
                <SelectValue placeholder="People Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All People</SelectItem>
                <SelectItem value="available">Available Only</SelectItem>
                <SelectItem value="busy">Busy</SelectItem>
                <SelectItem value="on-break">On Break</SelectItem>
                <SelectItem value="off-duty">Off Duty</SelectItem>
              </SelectContent>
            </Select>
            <Select value={equipmentFilter} onValueChange={setEquipmentFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Equipment Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Equipment</SelectItem>
                <SelectItem value="available">Available Only</SelectItem>
                <SelectItem value="in_use">In Use</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="repair">Repair</SelectItem>
                <SelectItem value="out_of_service">Out of Service</SelectItem>
              </SelectContent>
            </Select>
            <Select value={locationFilter} onValueChange={setLocationFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Locations" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Locations</SelectItem>
                <SelectItem value="depot">Depot</SelectItem>
                <SelectItem value="site">Job Sites</SelectItem>
                <SelectItem value="downtown">Downtown</SelectItem>
                <SelectItem value="shop">Shop</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* People Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-green-600" />
            Team Members ({filteredPeople.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredPeople.map((member) => (
              <div key={member.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h4 className="font-semibold text-gray-900">{member.name}</h4>
                    <p className="text-sm text-gray-600">{member.role}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${getStatusDot(member.status)}`}></div>
                    <Badge className={getStatusColor(member.status)}>
                      {member.status === "on-break"
                        ? "On Break"
                        : member.status === "off-duty"
                          ? "Off Duty"
                          : member.status.charAt(0).toUpperCase() + member.status.slice(1)}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2 text-sm text-gray-600 mb-3">
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    {member.location}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    Updated {member.lastUpdate}
                  </div>
                  {member.breakUntil && <div className="text-yellow-600">On break until {member.breakUntil}</div>}
                  {member.currentJob && <div className="text-blue-600">Working: {member.currentJob}</div>}
                  {member.availableNext && <div className="text-gray-600">Available: {member.availableNext}</div>}
                </div>

                <div className="flex flex-wrap gap-1 mb-3">
                  {member.skills.slice(0, 3).map((skill) => (
                    <Badge key={skill} variant="secondary" className="text-xs">
                      {skill}
                    </Badge>
                  ))}
                </div>

                <div className="flex items-center gap-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    <Phone className="h-3 w-3 mr-1" />
                    Call
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    <Mail className="h-3 w-3 mr-1" />
                    Message
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Equipment Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5 text-blue-600" />
            Equipment ({filteredEquipment.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredEquipment.map((item) => (
              <div key={item.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h4 className="font-semibold text-gray-900">{item.name}</h4>
                    <p className="text-sm text-gray-600">{item.type}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${getStatusDot(item.status)}`}></div>
                    <Badge className={getStatusColor(item.status)}>
                      {item.status === "in_use"
                        ? "In Use"
                        : item.status === "out_of_service"
                        ? "Out of Service"
                        : item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2 text-sm text-gray-600 mb-3">
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    {item.location}
                  </div>
                  <div className="flex items-center gap-1">
                    <Fuel className="h-3 w-3" />
                    Fuel: {item.fuel}
                  </div>
                  <div className="flex items-center gap-1">
                    <Wrench className="h-3 w-3" />
                    Last service: {item.lastService}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    Updated {item.lastUpdate}
                  </div>
                  {item.currentJob && <div className="text-blue-600">In use: {item.currentJob}</div>}
                  {item.availableAt && <div className="text-green-600">Available at: {item.availableAt}</div>}
                  {item.availableNext && <div className="text-gray-600">Available: {item.availableNext}</div>}
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-sm">
                    <span className="text-gray-500">Utilization: </span>
                    <span
                      className={`font-medium ${
                        Number.parseInt(item.utilization) > 80
                          ? "text-red-600"
                          : Number.parseInt(item.utilization) > 60
                            ? "text-yellow-600"
                            : "text-green-600"
                      }`}
                    >
                      {item.utilization}
                    </span>
                  </div>
                  <Button size="sm" variant="outline">
                    Track
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
