rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // DEVELOPMENT MODE: Allow all read/write access
    // TODO: Replace with proper authentication rules for production
    
    // Organizations collection
    match /organizations/{orgId} {
      // Only organization owners and admins can read/write organization details
      allow read: if hasOrgRole(orgId, ['owner', 'admin', 'manager', 'dispatcher', 'operator', 'viewer']);
      allow write: if hasOrgRole(orgId, ['owner', 'admin']);
      allow create: if isValidUser(); // Any authenticated user can create an organization
    }
    
    // Users collection
    match /users/{userId} {
      // Users can read their own profile
      allow read: if isValidUser() && (
        userId == getUserId() || 
        hasOrgRole(resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher'])
      );
      
      // Users can update their own profile (limited fields)
      allow update: if isValidUser() && userId == getUserId() && (
        !('role' in request.resource.data.diff(resource.data).affectedKeys()) &&
        !('organizationId' in request.resource.data.diff(resource.data).affectedKeys()) &&
        !('permissions' in request.resource.data.diff(resource.data).affectedKeys())
      );
      
      // Organization admins can create/update/delete users in their org
      allow create, update, delete: if isValidUser() && 
        hasOrgRole(request.resource.data.organizationId, ['owner', 'admin']);
    }
    
    // User-Organization relationships
    match /user_organizations/{docId} {
      allow read: if isValidUser() && (
        resource.data.userId == getUserId() ||
        hasOrgRole(resource.data.organizationId, ['owner', 'admin', 'manager'])
      );
      allow write: if hasOrgRole(resource.data.organizationId, ['owner', 'admin']);
    }
    
    // Equipment collection
    match /equipment/{equipmentId} {
      allow read: if hasOrgRole(resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher', 'operator', 'viewer']);
      allow write: if hasOrgRole(resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher']);
      allow create: if hasOrgRole(request.resource.data.organizationId, ['owner', 'admin', 'manager']);
    }
    
    // Jobs collection
    match /jobs/{jobId} {
      allow read: if hasOrgRole(resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher', 'operator', 'viewer']);
      allow write: if hasOrgRole(resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher']);
      allow create: if hasOrgRole(request.resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher']);
    }
    
    // Projects collection
    match /projects/{projectId} {
      allow read: if hasOrgRole(resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher', 'viewer']);
      allow write: if hasOrgRole(resource.data.organizationId, ['owner', 'admin', 'manager']);
      allow create: if hasOrgRole(request.resource.data.organizationId, ['owner', 'admin', 'manager']);
    }
    
    // Maintenance records
    match /maintenance_records/{recordId} {
      allow read: if hasOrgRole(resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher', 'operator', 'viewer']);
      allow write: if hasOrgRole(resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher']);
      allow create: if hasOrgRole(request.resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher']);
    }
    
    // Activity logs
    match /activity_logs/{logId} {
      allow read: if hasOrgRole(resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher']);
      allow create: if hasOrgRole(request.resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher', 'operator']);
      // Activity logs should not be updated or deleted to maintain audit trail
      allow update, delete: if false;
    }
    
    // Notifications
    match /notifications/{notificationId} {
      // Users can only read/update their own notifications
      allow read, update: if isValidUser() && resource.data.userId == getUserId();
      allow create: if hasOrgRole(request.resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher']);
      allow delete: if isValidUser() && resource.data.userId == getUserId();
    }
    
    // Dashboard metrics (read-only for most users)
    match /dashboard_metrics/{metricId} {
      allow read: if hasOrgRole(resource.data.organizationId, ['owner', 'admin', 'manager', 'dispatcher', 'viewer']);
      allow write: if hasOrgRole(resource.data.organizationId, ['owner', 'admin']);
    }

    // Team members collection (nested under organizations)
    match /organizations/{orgId}/teamMembers/{memberId} {
      allow read: if hasOrgRole(orgId, ['owner', 'admin', 'manager', 'dispatcher', 'operator', 'viewer']);
      allow write: if hasOrgRole(orgId, ['owner', 'admin', 'manager', 'dispatcher']);
      allow create: if hasOrgRole(orgId, ['owner', 'admin', 'manager']);
      allow delete: if hasOrgRole(orgId, ['owner', 'admin']);
    }

    // Development mode - allow all access for demo-org (REMOVE IN PRODUCTION)
    match /organizations/demo-org/{document=**} {
      allow read, write: if true;
    }

    // Default deny rule for any unmatched paths
    match /{document=**} {
      allow read, write: if false;
    }
  }
}