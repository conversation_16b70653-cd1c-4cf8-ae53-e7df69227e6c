// Test script to add sample team members
// Run this in the browser console on the team members page

async function testAddTeamMembers() {
  try {
    // Import Firebase functions dynamically
    const { collection, addDoc, Timestamp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
    
    // Get the db instance from the global scope (should be available from the page)
    const db = window.db || (await import('/lib/firebase/config.js')).db;
    
    if (!db) {
      console.error('❌ Firestore database not available');
      return;
    }

    const sampleMembers = [
      {
        profile: {
          firstName: '<PERSON>',
          lastName: '<PERSON>',
          avatar: null
        },
        position: 'Equipment Operator',
        department: 'Operations',
        email: '<EMAIL>',
        phone: '(*************',
        skills: {
          'excavator': true,
          'cdl-a': true,
          'osha-30': true
        },
        availability: {
          status: 'available',
          currentLocation: {
            name: 'Job Site A',
            coordinates: { lat: 40.7128, lng: -74.0060 }
          }
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        profile: {
          firstName: '<PERSON>',
          lastName: '<PERSON>',
          avatar: null
        },
        position: 'Site Supervisor',
        department: 'Management',
        email: '<EMAIL>',
        phone: '(*************',
        skills: {
          'project-management': true,
          'safety-inspector': true,
          'leadership': true
        },
        availability: {
          status: 'busy',
          currentLocation: {
            name: 'Highway 101',
            coordinates: { lat: 37.7749, lng: -122.4194 }
          }
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        profile: {
          firstName: 'Carlos',
          lastName: 'Rodriguez',
          avatar: null
        },
        position: 'Heavy Equipment Operator',
        department: 'Operations',
        email: '<EMAIL>',
        phone: '(*************',
        skills: {
          'crane-operator': true,
          'cdl-b': true,
          'forklift': true
        },
        availability: {
          status: 'available',
          currentLocation: {
            name: 'Depot',
            coordinates: { lat: 34.0522, lng: -118.2437 }
          }
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ];

    console.log('🌱 Adding sample team members...');
    
    for (const member of sampleMembers) {
      const docRef = await addDoc(
        collection(db, 'organizations', 'demo-org', 'teamMembers'),
        member
      );
      console.log(`✅ Added: ${member.profile.firstName} ${member.profile.lastName} (${docRef.id})`);
    }
    
    console.log('🎉 Sample team members added successfully!');
    console.log('The page should automatically refresh to show the new team members.');
    
  } catch (error) {
    console.error('❌ Error adding team members:', error);
  }
}

// Make function available globally
window.testAddTeamMembers = testAddTeamMembers;

console.log('📝 Run testAddTeamMembers() to add sample team member data');
