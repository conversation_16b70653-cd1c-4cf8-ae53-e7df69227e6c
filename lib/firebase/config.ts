// Firebase Configuration
import { initializeApp, getApps, FirebaseApp } from 'firebase/app'
import { getFirestore, Firestore } from 'firebase/firestore'
import { getAuth, Auth } from 'firebase/auth'

// Firebase configuration - using sandbox config for development
const firebaseConfig = {
  apiKey: "AIzaSyBU-toZ_dGAq4NOLaYU9UyA2BVcYZXWOY8",
  authDomain: "diggit-web-sbx.firebaseapp.com",
  projectId: "diggit-web-sbx",
  storageBucket: "diggit-web-sbx.firebasestorage.app",
  messagingSenderId: "278272232281",
  appId: "1:278272232281:web:712a8e5f092833e4595a48"
}

// For production, you would use environment variables:
// const firebaseConfig = {
//   apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
//   authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
//   projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
//   storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
//   messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
//   appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
// }

console.log('🔥 Firebase config loaded:', { projectId: firebaseConfig.projectId })

// Initialize Firebase
let app: FirebaseApp | undefined
let db: Firestore | undefined
let auth: Auth | undefined

// Only initialize in browser environment
if (typeof window !== 'undefined') {
  try {
    console.log('🔥 Initializing Firebase in browser...')

    // Initialize Firebase app (avoid re-initialization in development)
    if (!getApps().length) {
      app = initializeApp(firebaseConfig)
      console.log('✅ Firebase app initialized')
    } else {
      app = getApps()[0]
      console.log('✅ Firebase app already exists')
    }

    // Initialize Firebase services
    db = getFirestore(app)
    auth = getAuth(app)

    console.log('✅ Firebase services initialized')

  } catch (error) {
    console.error('❌ Firebase initialization error:', error)
  }
}

// Helper function to get Firebase app (for server-side use)
export function getFirebaseApp() {
  if (typeof window === 'undefined') {
    // Server-side initialization
    if (!getApps().length) {
      return initializeApp(firebaseConfig)
    } else {
      return getApps()[0]
    }
  }
  return app
}

// Helper function to get Firestore instance
export function getFirestoreDb() {
  if (typeof window === 'undefined') {
    // Server-side - create new instance
    const firebaseApp = getFirebaseApp()
    return getFirestore(firebaseApp)
  }
  return db
}

export { app, db, auth }
export default app