import { initializeApp, getApps, cert } from 'firebase-admin/app'
import { getFirestore } from 'firebase-admin/firestore'

// Check if Firebase credentials are available
const hasFirebaseCredentials = !!(
  process.env.FIREBASE_PROJECT_ID &&
  process.env.FIREBASE_CLIENT_EMAIL &&
  process.env.FIREBASE_PRIVATE_KEY
)

const firebaseAdminConfig = hasFirebaseCredentials ? {
  credential: cert({
    projectId: process.env.FIREBASE_PROJECT_ID,
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
    privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  }),
} : null

function createFirebaseAdminApp() {
  if (!hasFirebaseCredentials) {
    console.warn('Firebase credentials not found. Using mock data.')
    return null
  }

  if (getApps().length > 0) {
    return getApps()[0]!
  }
  return initializeApp(firebaseAdminConfig!)
}

export function getFirebaseAdmin() {
  return createFirebaseAdminApp()
}

const firebaseAdmin = createFirebaseAdminApp()
export const adminDb = firebaseAdmin ? getFirestore(firebaseAdmin) : null

export default firebaseAdmin