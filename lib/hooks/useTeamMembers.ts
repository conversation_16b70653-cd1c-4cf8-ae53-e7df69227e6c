"use client"

import { useState, useEffect } from 'react'
import { TeamMember } from '@/lib/types/firestore'
import { db } from '@/lib/firebase/config'
import {
  collection,
  query,
  where,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  orderBy,
  Timestamp
} from 'firebase/firestore'

interface UseTeamMembersOptions {
  organizationId?: string
  autoRefresh?: boolean
  refreshInterval?: number
  status?: string
  department?: string
}

interface UseTeamMembersReturn {
  teamMembers: TeamMember[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createTeamMember: (data: Partial<TeamMember>) => Promise<TeamMember | null>
  updateTeamMember: (id: string, data: Partial<TeamMember>) => Promise<TeamMember | null>
  deleteTeamMember: (id: string) => Promise<boolean>
  updateAvailability: (id: string, availability: Partial<TeamMember['availability']>) => Promise<TeamMember | null>
}

export function useTeamMembers(options: UseTeamMembersOptions = {}): UseTeamMembersReturn {
  const {
    organizationId = 'demo-org',
    autoRefresh = false,
    refreshInterval = 30000, // 30 seconds
    status,
    department
  } = options

  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchTeamMembers = async () => {
    try {
      setError(null)

      if (!db) {
        throw new Error('Firestore not initialized')
      }

      // Build query
      let q = query(
        collection(db, 'organizations', organizationId, 'teamMembers'),
        orderBy('profile.firstName', 'asc')
      )

      // Add filters if provided
      if (status) {
        q = query(q, where('availability.status', '==', status))
      }
      if (department) {
        q = query(q, where('department', '==', department))
      }

      const querySnapshot = await getDocs(q)
      const members: TeamMember[] = []

      querySnapshot.forEach((doc) => {
        members.push({
          id: doc.id,
          ...doc.data()
        } as TeamMember)
      })

      setTeamMembers(members)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      console.error('Error fetching team members:', err)
    } finally {
      setLoading(false)
    }
  }

  const createTeamMember = async (data: Partial<TeamMember>): Promise<TeamMember | null> => {
    try {
      setError(null)

      if (!db) {
        throw new Error('Firestore not initialized')
      }

      // Prepare the data with timestamps
      const teamMemberData = {
        ...data,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        // Ensure required fields have defaults
        availability: {
          status: 'available',
          ...data.availability
        },
        skills: data.skills || {},
        profile: {
          firstName: '',
          lastName: '',
          ...data.profile
        }
      }

      const docRef = await addDoc(
        collection(db, 'organizations', organizationId, 'teamMembers'),
        teamMemberData
      )

      const newMember = {
        id: docRef.id,
        ...teamMemberData
      } as TeamMember

      // Add to local state
      setTeamMembers(prev => [...prev, newMember])

      return newMember
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      console.error('Error creating team member:', err)
      return null
    }
  }

  const updateTeamMember = async (id: string, data: Partial<TeamMember>): Promise<TeamMember | null> => {
    try {
      setError(null)

      if (!db) {
        throw new Error('Firestore not initialized')
      }

      // Prepare update data with timestamp
      const updateData = {
        ...data,
        updatedAt: Timestamp.now()
      }

      const docRef = doc(db, 'organizations', organizationId, 'teamMembers', id)
      await updateDoc(docRef, updateData)

      // Update local state
      setTeamMembers(prev =>
        prev.map(member =>
          member.id === id ? { ...member, ...updateData } : member
        )
      )

      const updatedMember = teamMembers.find(m => m.id === id)
      return updatedMember ? { ...updatedMember, ...updateData } : null
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      console.error('Error updating team member:', err)
      return null
    }
  }

  const updateAvailability = async (id: string, availability: Partial<TeamMember['availability']>): Promise<TeamMember | null> => {
    return updateTeamMember(id, { availability })
  }

  const deleteTeamMember = async (id: string): Promise<boolean> => {
    try {
      setError(null)

      if (!db) {
        throw new Error('Firestore not initialized')
      }

      const docRef = doc(db, 'organizations', organizationId, 'teamMembers', id)
      await deleteDoc(docRef)

      // Remove from local state
      setTeamMembers(prev => prev.filter(member => member.id !== id))
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      console.error('Error deleting team member:', err)
      return false
    }
  }

  // Initial fetch
  useEffect(() => {
    fetchTeamMembers()
  }, [organizationId, status, department])

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(fetchTeamMembers, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, organizationId, status, department])

  return {
    teamMembers,
    loading,
    error,
    refetch: fetchTeamMembers,
    createTeamMember,
    updateTeamMember,
    deleteTeamMember,
    updateAvailability
  }
}
