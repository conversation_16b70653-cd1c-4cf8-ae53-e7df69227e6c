"use client"

import { useState, useEffect } from 'react'
import { TeamMember } from '@/lib/types/firestore'

interface UseTeamMembersOptions {
  organizationId?: string
  autoRefresh?: boolean
  refreshInterval?: number
  status?: string
  department?: string
}

interface UseTeamMembersReturn {
  teamMembers: TeamMember[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createTeamMember: (data: Partial<TeamMember>) => Promise<TeamMember | null>
  updateTeamMember: (id: string, data: Partial<TeamMember>) => Promise<TeamMember | null>
  deleteTeamMember: (id: string) => Promise<boolean>
  updateAvailability: (id: string, availability: Partial<TeamMember['availability']>) => Promise<TeamMember | null>
}

export function useTeamMembers(options: UseTeamMembersOptions = {}): UseTeamMembersReturn {
  const {
    organizationId = 'demo-org',
    autoRefresh = false,
    refreshInterval = 30000, // 30 seconds
    status,
    department
  } = options

  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchTeamMembers = async () => {
    try {
      setError(null)
      const params = new URLSearchParams({ orgId: organizationId })
      if (status) params.append('status', status)
      if (department) params.append('department', department)

      const response = await fetch(`/api/team-members?${params}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch team members')
      }

      if (result.success) {
        setTeamMembers(result.data || [])
      } else {
        throw new Error(result.error || 'Failed to fetch team members')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      console.error('Error fetching team members:', err)
    } finally {
      setLoading(false)
    }
  }

  const createTeamMember = async (data: Partial<TeamMember>): Promise<TeamMember | null> => {
    try {
      setError(null)
      const response = await fetch('/api/team-members', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          orgId: organizationId
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create team member')
      }

      if (result.success) {
        // Refresh the list to include the new member
        await fetchTeamMembers()
        return result.data
      } else {
        throw new Error(result.error || 'Failed to create team member')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      console.error('Error creating team member:', err)
      return null
    }
  }

  const updateTeamMember = async (id: string, data: Partial<TeamMember>): Promise<TeamMember | null> => {
    try {
      setError(null)
      const response = await fetch(`/api/team-members/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          orgId: organizationId
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update team member')
      }

      if (result.success) {
        // Update the local state
        setTeamMembers(prev => 
          prev.map(member => 
            member.id === id ? result.data : member
          )
        )
        return result.data
      } else {
        throw new Error(result.error || 'Failed to update team member')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      console.error('Error updating team member:', err)
      return null
    }
  }

  const updateAvailability = async (id: string, availability: Partial<TeamMember['availability']>): Promise<TeamMember | null> => {
    return updateTeamMember(id, { availability })
  }

  const deleteTeamMember = async (id: string): Promise<boolean> => {
    try {
      setError(null)
      const response = await fetch(`/api/team-members/${id}?orgId=${organizationId}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete team member')
      }

      if (result.success) {
        // Remove from local state
        setTeamMembers(prev => prev.filter(member => member.id !== id))
        return true
      } else {
        throw new Error(result.error || 'Failed to delete team member')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      console.error('Error deleting team member:', err)
      return false
    }
  }

  // Initial fetch
  useEffect(() => {
    fetchTeamMembers()
  }, [organizationId, status, department])

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(fetchTeamMembers, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, organizationId, status, department])

  return {
    teamMembers,
    loading,
    error,
    refetch: fetchTeamMembers,
    createTeamMember,
    updateTeamMember,
    deleteTeamMember,
    updateAvailability
  }
}
