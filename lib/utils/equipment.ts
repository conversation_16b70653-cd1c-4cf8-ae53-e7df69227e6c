import { Equipment, EquipmentStatus, EquipmentType } from '@/lib/types/firestore'

// Transform Firestore equipment data for UI display
export interface UIEquipment {
  id: string
  name: string
  type: string
  status: string
  location: string
  lastUpdate: string
  source: string
  operator: string
  make?: string
  model?: string
  year?: number
  currentHours?: number
  nextServiceDue?: string
}

export function transformEquipmentForUI(equipment: Equipment): UIEquipment {
  // Map Firestore status to UI status - preserve distinction between available and in_use
  const statusMap: Record<EquipmentStatus, string> = {
    'available': 'available',
    'in_use': 'in_use',
    'maintenance': 'maintenance',
    'repair': 'repair',
    'out_of_service': 'inactive'
  }

  // Map Firestore type to display type
  const typeMap: Record<EquipmentType, string> = {
    'excavator': 'Excavator',
    'dump_truck': 'Dump Truck',
    'backhoe': 'Backhoe',
    'bulldozer': 'Bulldozer',
    'crane': 'Crane',
    'compactor': 'Compactor',
    'loader': 'Loader',
    'grader': 'Motor Grader',
    'skid_steer': 'Skid Steer',
    'utility_truck': 'Utility Truck',
    'other': 'Other'
  }

  // Calculate time since last update
  const getTimeAgo = (timestamp: string | Date): string => {
    const now = new Date()
    const then = new Date(timestamp)
    const diffMs = now.getTime() - then.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins} min ago`
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
  }

  // Determine data source (for now, assume Samsara for active equipment, Manual for others)
  const getDataSource = (status: EquipmentStatus): string => {
    return ['available', 'in_use'].includes(status) ? 'Samsara' : 'Manual'
  }

  // Get operator info (placeholder for now)
  const getOperator = (status: EquipmentStatus): string => {
    if (status === 'in_use') {
      // In a real app, this would come from job assignments
      const operators = ['Mike Johnson', 'Carlos Rodriguez', 'Sarah Chen', 'David Kim']
      return operators[Math.floor(Math.random() * operators.length)]
    }
    return 'Unassigned'
  }

  // Get location display
  const getLocationDisplay = (equipment: Equipment): string => {
    if (equipment.currentLocation?.address) {
      return equipment.currentLocation.address
    }
    
    // Fallback based on status
    switch (equipment.status) {
      case 'maintenance':
      case 'repair':
        return 'Shop'
      case 'out_of_service':
        return 'Depot'
      case 'available':
        return 'Depot'
      case 'in_use':
        return `Job Site ${String.fromCharCode(65 + Math.floor(Math.random() * 3))}` // A, B, or C
      default:
        return 'Unknown'
    }
  }

  return {
    id: equipment.id,
    name: equipment.name,
    type: typeMap[equipment.type] || equipment.type,
    status: statusMap[equipment.status] || equipment.status,
    location: getLocationDisplay(equipment),
    lastUpdate: getTimeAgo(equipment.updatedAt || equipment.createdAt),
    source: getDataSource(equipment.status),
    operator: getOperator(equipment.status),
    make: equipment.make,
    model: equipment.model,
    year: equipment.year,
    currentHours: equipment.maintenance?.currentHours,
    nextServiceDue: equipment.maintenance?.nextServiceDue ?
      (typeof equipment.maintenance.nextServiceDue === 'string'
        ? new Date(equipment.maintenance.nextServiceDue).toLocaleDateString()
        : equipment.maintenance.nextServiceDue.seconds
          ? new Date(equipment.maintenance.nextServiceDue.seconds * 1000).toLocaleDateString()
          : new Date(equipment.maintenance.nextServiceDue).toLocaleDateString()
      ) : undefined
  }
}

// Status color utilities (matching the existing UI)
export const getStatusColor = (status: string) => {
  switch (status) {
    case "available":
      return "bg-green-100 text-green-800"
    case "in_use":
      return "bg-blue-100 text-blue-800"
    case "maintenance":
      return "bg-yellow-100 text-yellow-800"
    case "repair":
      return "bg-orange-100 text-orange-800"
    case "inactive":
    case "out_of_service":
      return "bg-red-100 text-red-800"
    // Legacy support for old "active" status
    case "active":
      return "bg-green-100 text-green-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

export const getStatusDot = (status: string) => {
  switch (status) {
    case "available":
      return "bg-green-400"
    case "in_use":
      return "bg-blue-400"
    case "maintenance":
      return "bg-yellow-400"
    case "repair":
      return "bg-orange-400"
    case "inactive":
    case "out_of_service":
      return "bg-red-400"
    // Legacy support for old "active" status
    case "active":
      return "bg-green-400"
    default:
      return "bg-gray-400"
  }
}

// Get user-friendly status display text
export const getStatusDisplayText = (status: string) => {
  switch (status) {
    case "available":
      return "Available"
    case "in_use":
      return "In Use"
    case "maintenance":
      return "Maintenance"
    case "repair":
      return "Repair"
    case "inactive":
      return "Inactive"
    case "out_of_service":
      return "Out of Service"
    // Legacy support
    case "active":
      return "Active"
    default:
      return status.charAt(0).toUpperCase() + status.slice(1)
  }
}

// Filter utilities
export function filterEquipment(
  equipment: UIEquipment[], 
  filters: {
    status?: string
    source?: string
    search?: string
  }
): UIEquipment[] {
  return equipment.filter((item) => {
    const matchesStatus = !filters.status || filters.status === "all" || item.status === filters.status
    const matchesSource = !filters.source || filters.source === "all" || item.source.toLowerCase() === filters.source
    const matchesSearch = !filters.search || 
      item.name.toLowerCase().includes(filters.search.toLowerCase()) ||
      item.type.toLowerCase().includes(filters.search.toLowerCase())

    return matchesStatus && matchesSource && matchesSearch
  })
}

// Equipment statistics
export function getEquipmentStats(equipment: UIEquipment[]) {
  const total = equipment.length
  const available = equipment.filter(e => e.status === 'available').length
  const inUse = equipment.filter(e => e.status === 'in_use').length
  const maintenance = equipment.filter(e => e.status === 'maintenance' || e.status === 'repair').length
  const inactive = equipment.filter(e => e.status === 'inactive' || e.status === 'out_of_service').length

  // For backward compatibility, calculate "active" as available + in_use
  const active = available + inUse

  return {
    total,
    available,
    inUse,
    active, // Legacy field
    maintenance,
    inactive,
    utilization: total > 0 ? Math.round((inUse / total) * 100) : 0 // Utilization based on in_use only
  }
}

// Equipment type options for forms
export const equipmentTypeOptions = [
  { value: 'excavator', label: 'Excavator' },
  { value: 'dump_truck', label: 'Dump Truck' },
  { value: 'backhoe', label: 'Backhoe' },
  { value: 'bulldozer', label: 'Bulldozer' },
  { value: 'crane', label: 'Crane' },
  { value: 'compactor', label: 'Compactor' },
  { value: 'loader', label: 'Loader' },
  { value: 'grader', label: 'Motor Grader' },
  { value: 'skid_steer', label: 'Skid Steer' },
  { value: 'utility_truck', label: 'Utility Truck' },
  { value: 'other', label: 'Other' }
]

// Equipment status options for forms
export const equipmentStatusOptions = [
  { value: 'available', label: 'Available' },
  { value: 'in_use', label: 'In Use' },
  { value: 'maintenance', label: 'Maintenance' },
  { value: 'repair', label: 'Repair' },
  { value: 'out_of_service', label: 'Out of Service' }
]
