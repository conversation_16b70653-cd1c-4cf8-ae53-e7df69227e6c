// Run this in the browser console on the team members page to add sample data
async function addSampleTeamMembers() {
  // Import Firebase functions
  const { collection, addDoc, Timestamp } = await import('firebase/firestore');
  const { db } = await import('/lib/firebase/config.js');
  
  if (!db) {
    console.error('Firestore not initialized');
    return;
  }

  const sampleMembers = [
    {
      profile: {
        firstName: 'Mike',
        lastName: 'Johnson',
        avatar: null
      },
      position: 'Equipment Operator',
      department: 'Operations',
      email: '<EMAIL>',
      phone: '(*************',
      skills: {
        'excavator': true,
        'cdl-a': true,
        'osha-30': true
      },
      availability: {
        status: 'available',
        currentLocation: {
          name: 'Job Site A',
          coordinates: { lat: 40.7128, lng: -74.0060 }
        }
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    },
    {
      profile: {
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        avatar: null
      },
      position: 'Site Supervisor',
      department: 'Management',
      email: '<EMAIL>',
      phone: '(*************',
      skills: {
        'project-management': true,
        'safety-inspector': true,
        'leadership': true
      },
      availability: {
        status: 'busy',
        currentLocation: {
          name: 'Highway 101',
          coordinates: { lat: 37.7749, lng: -122.4194 }
        }
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    },
    {
      profile: {
        firstName: 'Carlos',
        lastName: 'Rodriguez',
        avatar: null
      },
      position: 'Heavy Equipment Operator',
      department: 'Operations',
      email: '<EMAIL>',
      phone: '(*************',
      skills: {
        'crane-operator': true,
        'cdl-b': true,
        'forklift': true
      },
      availability: {
        status: 'available',
        currentLocation: {
          name: 'Depot',
          coordinates: { lat: 34.0522, lng: -118.2437 }
        }
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    },
    {
      profile: {
        firstName: 'David',
        lastName: 'Kim',
        avatar: null
      },
      position: 'Maintenance Technician',
      department: 'Maintenance',
      email: '<EMAIL>',
      phone: '(*************',
      skills: {
        'hydraulics': true,
        'electrical': true,
        'diagnostics': true
      },
      availability: {
        status: 'busy',
        currentLocation: {
          name: 'Shop',
          coordinates: { lat: 41.8781, lng: -87.6298 }
        }
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  ];

  try {
    console.log('🌱 Adding sample team members...');
    
    for (const member of sampleMembers) {
      const docRef = await addDoc(
        collection(db, 'organizations', 'demo-org', 'teamMembers'),
        member
      );
      console.log(`✅ Added: ${member.profile.firstName} ${member.profile.lastName} (${docRef.id})`);
    }
    
    console.log('🎉 Sample team members added successfully!');
    console.log('Refresh the page to see the new team members.');
  } catch (error) {
    console.error('❌ Error adding team members:', error);
  }
}

// Make function available globally
window.addSampleTeamMembers = addSampleTeamMembers;

console.log('📝 Run addSampleTeamMembers() to add sample team member data');
