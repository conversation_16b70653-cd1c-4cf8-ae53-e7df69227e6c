import { db } from '../lib/firebase/config'
import { collection, addDoc, Timestamp } from 'firebase/firestore'

const sampleTeamMembers = [
  {
    profile: {
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      avatar: null
    },
    position: 'Equipment Operator',
    department: 'Operations',
    email: '<EMAIL>',
    phone: '(*************',
    skills: {
      'excavator': true,
      'cdl-a': true,
      'osha-30': true
    },
    availability: {
      status: 'available',
      currentLocation: {
        name: 'Job Site A',
        coordinates: { lat: 40.7128, lng: -74.0060 }
      }
    },
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  },
  {
    profile: {
      firstName: 'Sarah',
      lastName: 'Chen',
      avatar: null
    },
    position: 'Site Supervisor',
    department: 'Management',
    email: '<EMAIL>',
    phone: '(*************',
    skills: {
      'project-management': true,
      'safety-inspector': true,
      'leadership': true
    },
    availability: {
      status: 'busy',
      currentLocation: {
        name: 'Highway 101',
        coordinates: { lat: 37.7749, lng: -122.4194 }
      }
    },
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  },
  {
    profile: {
      firstName: '<PERSON>',
      lastName: 'Rodriguez',
      avatar: null
    },
    position: 'Heavy Equipment Operator',
    department: 'Operations',
    email: '<EMAIL>',
    phone: '(*************',
    skills: {
      'crane-operator': true,
      'cdl-b': true,
      'forklift': true
    },
    availability: {
      status: 'available',
      currentLocation: {
        name: 'Depot',
        coordinates: { lat: 34.0522, lng: -118.2437 }
      }
    },
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  },
  {
    profile: {
      firstName: 'David',
      lastName: 'Kim',
      avatar: null
    },
    position: 'Maintenance Technician',
    department: 'Maintenance',
    email: '<EMAIL>',
    phone: '(*************',
    skills: {
      'hydraulics': true,
      'electrical': true,
      'diagnostics': true
    },
    availability: {
      status: 'busy',
      currentLocation: {
        name: 'Shop',
        coordinates: { lat: 41.8781, lng: -87.6298 }
      }
    },
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now()
  }
]

export async function seedTeamMembers() {
  if (!db) {
    console.error('Firestore not initialized')
    return
  }

  try {
    console.log('🌱 Seeding team members...')
    
    for (const member of sampleTeamMembers) {
      const docRef = await addDoc(
        collection(db, 'organizations', 'demo-org', 'teamMembers'),
        member
      )
      console.log(`✅ Added team member: ${member.profile.firstName} ${member.profile.lastName} (${docRef.id})`)
    }
    
    console.log('🎉 Team members seeded successfully!')
  } catch (error) {
    console.error('❌ Error seeding team members:', error)
  }
}

// Run if called directly
if (typeof window !== 'undefined') {
  // Browser environment - can be called from console
  (window as any).seedTeamMembers = seedTeamMembers
}
