"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/fleet/page",{

/***/ "(app-pages-browser)/./lib/utils/equipment.ts":
/*!********************************!*\
  !*** ./lib/utils/equipment.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   equipmentStatusOptions: () => (/* binding */ equipmentStatusOptions),\n/* harmony export */   equipmentTypeOptions: () => (/* binding */ equipmentTypeOptions),\n/* harmony export */   filterEquipment: () => (/* binding */ filterEquipment),\n/* harmony export */   getEquipmentStats: () => (/* binding */ getEquipmentStats),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getStatusDot: () => (/* binding */ getStatusDot),\n/* harmony export */   transformEquipmentForUI: () => (/* binding */ transformEquipmentForUI)\n/* harmony export */ });\nfunction transformEquipmentForUI(equipment) {\n    var _equipment_maintenance, _equipment_maintenance1;\n    // Map Firestore status to UI status - preserve distinction between available and in_use\n    const statusMap = {\n        'available': 'available',\n        'in_use': 'in_use',\n        'maintenance': 'maintenance',\n        'repair': 'repair',\n        'out_of_service': 'inactive'\n    };\n    // Map Firestore type to display type\n    const typeMap = {\n        'excavator': 'Excavator',\n        'dump_truck': 'Dump Truck',\n        'backhoe': 'Backhoe',\n        'bulldozer': 'Bulldozer',\n        'crane': 'Crane',\n        'compactor': 'Compactor',\n        'loader': 'Loader',\n        'grader': 'Motor Grader',\n        'skid_steer': 'Skid Steer',\n        'utility_truck': 'Utility Truck',\n        'other': 'Other'\n    };\n    // Calculate time since last update\n    const getTimeAgo = (timestamp)=>{\n        const now = new Date();\n        const then = new Date(timestamp);\n        const diffMs = now.getTime() - then.getTime();\n        const diffMins = Math.floor(diffMs / (1000 * 60));\n        const diffHours = Math.floor(diffMins / 60);\n        const diffDays = Math.floor(diffHours / 24);\n        if (diffMins < 1) return 'Just now';\n        if (diffMins < 60) return \"\".concat(diffMins, \" min ago\");\n        if (diffHours < 24) return \"\".concat(diffHours, \" hour\").concat(diffHours > 1 ? 's' : '', \" ago\");\n        return \"\".concat(diffDays, \" day\").concat(diffDays > 1 ? 's' : '', \" ago\");\n    };\n    // Determine data source (for now, assume Samsara for active equipment, Manual for others)\n    const getDataSource = (status)=>{\n        return [\n            'available',\n            'in_use'\n        ].includes(status) ? 'Samsara' : 'Manual';\n    };\n    // Get operator info (placeholder for now)\n    const getOperator = (status)=>{\n        if (status === 'in_use') {\n            // In a real app, this would come from job assignments\n            const operators = [\n                'Mike Johnson',\n                'Carlos Rodriguez',\n                'Sarah Chen',\n                'David Kim'\n            ];\n            return operators[Math.floor(Math.random() * operators.length)];\n        }\n        return 'Unassigned';\n    };\n    // Get location display\n    const getLocationDisplay = (equipment)=>{\n        var _equipment_currentLocation;\n        if ((_equipment_currentLocation = equipment.currentLocation) === null || _equipment_currentLocation === void 0 ? void 0 : _equipment_currentLocation.address) {\n            return equipment.currentLocation.address;\n        }\n        // Fallback based on status\n        switch(equipment.status){\n            case 'maintenance':\n            case 'repair':\n                return 'Shop';\n            case 'out_of_service':\n                return 'Depot';\n            case 'available':\n                return 'Depot';\n            case 'in_use':\n                return \"Job Site \".concat(String.fromCharCode(65 + Math.floor(Math.random() * 3))) // A, B, or C\n                ;\n            default:\n                return 'Unknown';\n        }\n    };\n    return {\n        id: equipment.id,\n        name: equipment.name,\n        type: typeMap[equipment.type] || equipment.type,\n        status: statusMap[equipment.status] || equipment.status,\n        location: getLocationDisplay(equipment),\n        lastUpdate: getTimeAgo(equipment.updatedAt || equipment.createdAt),\n        source: getDataSource(equipment.status),\n        operator: getOperator(equipment.status),\n        make: equipment.make,\n        model: equipment.model,\n        year: equipment.year,\n        currentHours: (_equipment_maintenance = equipment.maintenance) === null || _equipment_maintenance === void 0 ? void 0 : _equipment_maintenance.currentHours,\n        nextServiceDue: ((_equipment_maintenance1 = equipment.maintenance) === null || _equipment_maintenance1 === void 0 ? void 0 : _equipment_maintenance1.nextServiceDue) ? typeof equipment.maintenance.nextServiceDue === 'string' ? new Date(equipment.maintenance.nextServiceDue).toLocaleDateString() : equipment.maintenance.nextServiceDue.seconds ? new Date(equipment.maintenance.nextServiceDue.seconds * 1000).toLocaleDateString() : new Date(equipment.maintenance.nextServiceDue).toLocaleDateString() : undefined\n    };\n}\n// Status color utilities (matching the existing UI)\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"available\":\n            return \"bg-green-100 text-green-800\";\n        case \"in_use\":\n            return \"bg-blue-100 text-blue-800\";\n        case \"maintenance\":\n            return \"bg-yellow-100 text-yellow-800\";\n        case \"repair\":\n            return \"bg-orange-100 text-orange-800\";\n        case \"inactive\":\n        case \"out_of_service\":\n            return \"bg-red-100 text-red-800\";\n        // Legacy support for old \"active\" status\n        case \"active\":\n            return \"bg-green-100 text-green-800\";\n        default:\n            return \"bg-gray-100 text-gray-800\";\n    }\n};\nconst getStatusDot = (status)=>{\n    switch(status){\n        case \"available\":\n            return \"bg-green-400\";\n        case \"in_use\":\n            return \"bg-blue-400\";\n        case \"maintenance\":\n            return \"bg-yellow-400\";\n        case \"repair\":\n            return \"bg-orange-400\";\n        case \"inactive\":\n        case \"out_of_service\":\n            return \"bg-red-400\";\n        // Legacy support for old \"active\" status\n        case \"active\":\n            return \"bg-green-400\";\n        default:\n            return \"bg-gray-400\";\n    }\n};\n// Filter utilities\nfunction filterEquipment(equipment, filters) {\n    return equipment.filter((item)=>{\n        const matchesStatus = !filters.status || filters.status === \"all\" || item.status === filters.status;\n        const matchesSource = !filters.source || filters.source === \"all\" || item.source.toLowerCase() === filters.source;\n        const matchesSearch = !filters.search || item.name.toLowerCase().includes(filters.search.toLowerCase()) || item.type.toLowerCase().includes(filters.search.toLowerCase());\n        return matchesStatus && matchesSource && matchesSearch;\n    });\n}\n// Equipment statistics\nfunction getEquipmentStats(equipment) {\n    const total = equipment.length;\n    const available = equipment.filter((e)=>e.status === 'available').length;\n    const inUse = equipment.filter((e)=>e.status === 'in_use').length;\n    const maintenance = equipment.filter((e)=>e.status === 'maintenance' || e.status === 'repair').length;\n    const inactive = equipment.filter((e)=>e.status === 'inactive' || e.status === 'out_of_service').length;\n    // For backward compatibility, calculate \"active\" as available + in_use\n    const active = available + inUse;\n    return {\n        total,\n        available,\n        inUse,\n        active,\n        maintenance,\n        inactive,\n        utilization: total > 0 ? Math.round(inUse / total * 100) : 0 // Utilization based on in_use only\n    };\n}\n// Equipment type options for forms\nconst equipmentTypeOptions = [\n    {\n        value: 'excavator',\n        label: 'Excavator'\n    },\n    {\n        value: 'dump_truck',\n        label: 'Dump Truck'\n    },\n    {\n        value: 'backhoe',\n        label: 'Backhoe'\n    },\n    {\n        value: 'bulldozer',\n        label: 'Bulldozer'\n    },\n    {\n        value: 'crane',\n        label: 'Crane'\n    },\n    {\n        value: 'compactor',\n        label: 'Compactor'\n    },\n    {\n        value: 'loader',\n        label: 'Loader'\n    },\n    {\n        value: 'grader',\n        label: 'Motor Grader'\n    },\n    {\n        value: 'skid_steer',\n        label: 'Skid Steer'\n    },\n    {\n        value: 'utility_truck',\n        label: 'Utility Truck'\n    },\n    {\n        value: 'other',\n        label: 'Other'\n    }\n];\n// Equipment status options for forms\nconst equipmentStatusOptions = [\n    {\n        value: 'available',\n        label: 'Available'\n    },\n    {\n        value: 'in_use',\n        label: 'In Use'\n    },\n    {\n        value: 'maintenance',\n        label: 'Maintenance'\n    },\n    {\n        value: 'repair',\n        label: 'Repair'\n    },\n    {\n        value: 'out_of_service',\n        label: 'Out of Service'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/utils/equipment.ts\n"));

/***/ })

});