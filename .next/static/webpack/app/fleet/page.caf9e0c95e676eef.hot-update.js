"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/fleet/page",{

/***/ "(app-pages-browser)/./app/fleet/page.tsx":
/*!****************************!*\
  !*** ./app/fleet/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FleetOverviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useEquipment */ \"(app-pages-browser)/./lib/hooks/useEquipment.ts\");\n/* harmony import */ var _lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils/equipment */ \"(app-pages-browser)/./lib/utils/equipment.ts\");\n/* harmony import */ var _components_fleet_EquipmentModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/fleet/EquipmentModal */ \"(app-pages-browser)/./components/fleet/EquipmentModal.tsx\");\n/* harmony import */ var _components_fleet_EquipmentDetailsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/fleet/EquipmentDetailsModal */ \"(app-pages-browser)/./components/fleet/EquipmentDetailsModal.tsx\");\n/* harmony import */ var _components_fleet_DeleteConfirmDialog__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/fleet/DeleteConfirmDialog */ \"(app-pages-browser)/./components/fleet/DeleteConfirmDialog.tsx\");\n/* harmony import */ var _components_ui_equipment_location_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/equipment-location-modal */ \"(app-pages-browser)/./components/ui/equipment-location-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Component now uses real Firestore data via useEquipment hook\nfunction FleetOverviewPage() {\n    _s();\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sourceFilter, setSourceFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Modal states\n    const [isEquipmentModalOpen, setIsEquipmentModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDetailsModalOpen, setIsDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLocationModalOpen, setIsLocationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEquipment, setSelectedEquipment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modalMode, setModalMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('create');\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    // Use the custom hook to fetch equipment data\n    const { equipment: rawEquipment, loading, error, refetch, createEquipment, updateEquipment, deleteEquipment } = (0,_lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__.useEquipment)({\n        organizationId: 'demo-org',\n        autoRefresh: true,\n        refreshInterval: 30000 // Refresh every 30 seconds\n    });\n    // Transform Firestore data for UI display\n    const equipmentData = rawEquipment.map(_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.transformEquipmentForUI);\n    // Apply filters\n    const filteredEquipment = (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.filterEquipment)(equipmentData, {\n        status: statusFilter === \"all\" ? undefined : statusFilter,\n        source: sourceFilter === \"all\" ? undefined : sourceFilter,\n        search: searchTerm || undefined\n    });\n    // Get equipment statistics\n    const stats = (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getEquipmentStats)(equipmentData);\n    // Get status counts for filter dropdown\n    const getStatusCounts = ()=>{\n        const counts = {};\n        equipmentData.forEach((equipment)=>{\n            counts[equipment.status] = (counts[equipment.status] || 0) + 1;\n        });\n        return counts;\n    };\n    const statusCounts = getStatusCounts();\n    // CRUD handlers\n    const handleAddEquipment = ()=>{\n        setSelectedEquipment(null);\n        setModalMode('create');\n        setIsEquipmentModalOpen(true);\n    };\n    const handleEditEquipment = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setModalMode('edit');\n        setIsEquipmentModalOpen(true);\n    };\n    const handleViewDetails = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsDetailsModalOpen(true);\n    };\n    const handleDeleteEquipment = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsDeleteDialogOpen(true);\n    };\n    const handleSaveEquipment = async (data)=>{\n        try {\n            if (modalMode === 'create') {\n                await createEquipment(data);\n                toast({\n                    title: \"Equipment Added\",\n                    description: \"\".concat(data.name, \" has been successfully added to your fleet.\")\n                });\n            } else if (selectedEquipment) {\n                await updateEquipment(selectedEquipment.id, data);\n                toast({\n                    title: \"Equipment Updated\",\n                    description: \"\".concat(data.name, \" has been successfully updated.\")\n                });\n            }\n            setIsEquipmentModalOpen(false);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save equipment. Please try again.\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const handleConfirmDelete = async ()=>{\n        if (!selectedEquipment) return;\n        try {\n            await deleteEquipment(selectedEquipment.id);\n            toast({\n                title: \"Equipment Deleted\",\n                description: \"\".concat(selectedEquipment.name, \" has been removed from your fleet.\")\n            });\n            setIsDeleteDialogOpen(false);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete equipment. Please try again.\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const handleScheduleMaintenance = (equipment)=>{\n        // TODO: Implement maintenance scheduling\n        toast({\n            title: \"Feature Coming Soon\",\n            description: \"Maintenance scheduling will be available in a future update.\"\n        });\n    };\n    const handleTrackLocation = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsLocationModalOpen(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fleet Overview\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Manage and monitor your equipment fleet\",\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600 ml-2\",\n                                        children: \"• Loading...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 25\n                                    }, this),\n                                    !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600 ml-2\",\n                                        children: \"• Live from Firestore\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 26\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: refetch,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleAddEquipment,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Equipment\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-red-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Error loading equipment: \",\n                                    error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: refetch,\n                                className: \"ml-auto\",\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"Search equipment...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: statusFilter,\n                                onValueChange: setStatusFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-full md:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: [\n                                                    \"All Status (\",\n                                                    equipmentData.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            _lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.equipmentStatusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: option.value,\n                                                    children: [\n                                                        option.label,\n                                                        \" (\",\n                                                        statusCounts[option.value] || 0,\n                                                        \")\"\n                                                    ]\n                                                }, option.value, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: sourceFilter,\n                                onValueChange: setSourceFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-full md:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Source\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Sources\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"samsara\",\n                                                children: \"Samsara\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"manual\",\n                                                children: \"Manual\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Equipment (\",\n                                        filteredEquipment.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Active (\",\n                                                stats.active,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Maintenance (\",\n                                                stats.maintenance,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Inactive (\",\n                                                stats.inactive,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400 ml-2\",\n                                            children: [\n                                                \"Utilization: \",\n                                                stats.utilization,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6 animate-spin text-gray-400 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Loading equipment...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this) : filteredEquipment.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: equipmentData.length === 0 ? \"No equipment found. Try adding some equipment or seeding demo data.\" : \"No equipment matches your current filters.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this),\n                                equipmentData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"mt-4\",\n                                    onClick: ()=>window.open('/firestore-demo', '_blank'),\n                                    children: \"Seed Demo Data\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Equipment Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Location\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Operator\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Last Update\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Source\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                    children: filteredEquipment.map((equipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                            className: \"hover:bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    className: \"font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: equipment.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.make && equipment.model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    equipment.make,\n                                                                    \" \",\n                                                                    equipment.model,\n                                                                    \" \",\n                                                                    equipment.year\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: equipment.type\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat((0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusDot)(equipment.status))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusColor)(equipment.status),\n                                                                children: (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusDisplayText)(equipment.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.location\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: equipment.operator\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-3 w-3 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.lastUpdate\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: equipment.source\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                                                align: \"end\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleViewDetails(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"View Details\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleEditEquipment(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Edit Equipment\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleScheduleMaintenance(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 348,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Schedule Maintenance\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleTrackLocation(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Track Location\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleDeleteEquipment(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        className: \"text-red-600 focus:text-red-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Delete Equipment\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, equipment.id, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_EquipmentModal__WEBPACK_IMPORTED_MODULE_11__.EquipmentModal, {\n                isOpen: isEquipmentModalOpen,\n                onClose: ()=>setIsEquipmentModalOpen(false),\n                onSave: handleSaveEquipment,\n                equipment: selectedEquipment,\n                mode: modalMode\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_EquipmentDetailsModal__WEBPACK_IMPORTED_MODULE_12__.EquipmentDetailsModal, {\n                isOpen: isDetailsModalOpen,\n                onClose: ()=>setIsDetailsModalOpen(false),\n                equipment: selectedEquipment,\n                onEdit: handleEditEquipment,\n                onDelete: handleDeleteEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 383,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_DeleteConfirmDialog__WEBPACK_IMPORTED_MODULE_13__.DeleteConfirmDialog, {\n                isOpen: isDeleteDialogOpen,\n                onClose: ()=>setIsDeleteDialogOpen(false),\n                onConfirm: handleConfirmDelete,\n                equipment: selectedEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_equipment_location_modal__WEBPACK_IMPORTED_MODULE_14__.EquipmentLocationModal, {\n                isOpen: isLocationModalOpen,\n                onClose: ()=>setIsLocationModalOpen(false),\n                equipment: rawEquipment,\n                selectedEquipment: selectedEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(FleetOverviewPage, \"c8RWddfWYkkFPXLK8LvPJ0kfOzg=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast,\n        _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__.useEquipment\n    ];\n});\n_c = FleetOverviewPage;\nvar _c;\n$RefreshReg$(_c, \"FleetOverviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/fleet/page.tsx\n"));

/***/ })

});