"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/fleet/page",{

/***/ "(app-pages-browser)/./app/fleet/page.tsx":
/*!****************************!*\
  !*** ./app/fleet/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FleetOverviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useEquipment */ \"(app-pages-browser)/./lib/hooks/useEquipment.ts\");\n/* harmony import */ var _lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils/equipment */ \"(app-pages-browser)/./lib/utils/equipment.ts\");\n/* harmony import */ var _components_fleet_EquipmentModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/fleet/EquipmentModal */ \"(app-pages-browser)/./components/fleet/EquipmentModal.tsx\");\n/* harmony import */ var _components_fleet_EquipmentDetailsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/fleet/EquipmentDetailsModal */ \"(app-pages-browser)/./components/fleet/EquipmentDetailsModal.tsx\");\n/* harmony import */ var _components_fleet_DeleteConfirmDialog__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/fleet/DeleteConfirmDialog */ \"(app-pages-browser)/./components/fleet/DeleteConfirmDialog.tsx\");\n/* harmony import */ var _components_ui_equipment_location_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/equipment-location-modal */ \"(app-pages-browser)/./components/ui/equipment-location-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Component now uses real Firestore data via useEquipment hook\nfunction FleetOverviewPage() {\n    _s();\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sourceFilter, setSourceFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Modal states\n    const [isEquipmentModalOpen, setIsEquipmentModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDetailsModalOpen, setIsDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLocationModalOpen, setIsLocationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEquipment, setSelectedEquipment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modalMode, setModalMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('create');\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    // Use the custom hook to fetch equipment data\n    const { equipment: rawEquipment, loading, error, refetch, createEquipment, updateEquipment, deleteEquipment } = (0,_lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__.useEquipment)({\n        organizationId: 'demo-org',\n        autoRefresh: true,\n        refreshInterval: 30000 // Refresh every 30 seconds\n    });\n    // Transform Firestore data for UI display\n    const equipmentData = rawEquipment.map(_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.transformEquipmentForUI);\n    // Apply filters\n    const filteredEquipment = (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.filterEquipment)(equipmentData, {\n        status: statusFilter === \"all\" ? undefined : statusFilter,\n        source: sourceFilter === \"all\" ? undefined : sourceFilter,\n        search: searchTerm || undefined\n    });\n    // Get equipment statistics\n    const stats = (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getEquipmentStats)(equipmentData);\n    // Get status counts for filter dropdown\n    const getStatusCounts = ()=>{\n        const counts = {};\n        equipmentData.forEach((equipment)=>{\n            counts[equipment.status] = (counts[equipment.status] || 0) + 1;\n        });\n        return counts;\n    };\n    const statusCounts = getStatusCounts();\n    // CRUD handlers\n    const handleAddEquipment = ()=>{\n        setSelectedEquipment(null);\n        setModalMode('create');\n        setIsEquipmentModalOpen(true);\n    };\n    const handleEditEquipment = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setModalMode('edit');\n        setIsEquipmentModalOpen(true);\n    };\n    const handleViewDetails = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsDetailsModalOpen(true);\n    };\n    const handleDeleteEquipment = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsDeleteDialogOpen(true);\n    };\n    const handleSaveEquipment = async (data)=>{\n        try {\n            if (modalMode === 'create') {\n                await createEquipment(data);\n                toast({\n                    title: \"Equipment Added\",\n                    description: \"\".concat(data.name, \" has been successfully added to your fleet.\")\n                });\n            } else if (selectedEquipment) {\n                await updateEquipment(selectedEquipment.id, data);\n                toast({\n                    title: \"Equipment Updated\",\n                    description: \"\".concat(data.name, \" has been successfully updated.\")\n                });\n            }\n            setIsEquipmentModalOpen(false);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save equipment. Please try again.\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const handleConfirmDelete = async ()=>{\n        if (!selectedEquipment) return;\n        try {\n            await deleteEquipment(selectedEquipment.id);\n            toast({\n                title: \"Equipment Deleted\",\n                description: \"\".concat(selectedEquipment.name, \" has been removed from your fleet.\")\n            });\n            setIsDeleteDialogOpen(false);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete equipment. Please try again.\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const handleScheduleMaintenance = (equipment)=>{\n        // TODO: Implement maintenance scheduling\n        toast({\n            title: \"Feature Coming Soon\",\n            description: \"Maintenance scheduling will be available in a future update.\"\n        });\n    };\n    const handleTrackLocation = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsLocationModalOpen(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fleet Overview\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Manage and monitor your equipment fleet\",\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600 ml-2\",\n                                        children: \"• Loading...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 25\n                                    }, this),\n                                    !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600 ml-2\",\n                                        children: \"• Live from Firestore\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 26\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: refetch,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleAddEquipment,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Equipment\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-red-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Error loading equipment: \",\n                                    error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: refetch,\n                                className: \"ml-auto\",\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"Search equipment...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: statusFilter,\n                                onValueChange: setStatusFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-full md:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: [\n                                                    \"All Status (\",\n                                                    equipmentData.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            _lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.equipmentStatusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: option.value,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat((0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusDot)(option.value))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    option.label,\n                                                                    \" (\",\n                                                                    statusCounts[option.value] || 0,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: sourceFilter,\n                                onValueChange: setSourceFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-full md:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Source\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Sources\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"samsara\",\n                                                children: \"Samsara\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"manual\",\n                                                children: \"Manual\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Equipment (\",\n                                        filteredEquipment.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Active (\",\n                                                stats.active,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Maintenance (\",\n                                                stats.maintenance,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Inactive (\",\n                                                stats.inactive,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400 ml-2\",\n                                            children: [\n                                                \"Utilization: \",\n                                                stats.utilization,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6 animate-spin text-gray-400 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Loading equipment...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this) : filteredEquipment.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: equipmentData.length === 0 ? \"No equipment found. Try adding some equipment or seeding demo data.\" : \"No equipment matches your current filters.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this),\n                                equipmentData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"mt-4\",\n                                    onClick: ()=>window.open('/firestore-demo', '_blank'),\n                                    children: \"Seed Demo Data\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Equipment Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Location\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Operator\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Last Update\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Source\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                    children: filteredEquipment.map((equipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                            className: \"hover:bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    className: \"font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: equipment.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.make && equipment.model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    equipment.make,\n                                                                    \" \",\n                                                                    equipment.model,\n                                                                    \" \",\n                                                                    equipment.year\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: equipment.type\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat((0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusDot)(equipment.status))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusColor)(equipment.status),\n                                                                children: (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusDisplayText)(equipment.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.location\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: equipment.operator\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-3 w-3 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.lastUpdate\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: equipment.source\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                                                align: \"end\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleViewDetails(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 342,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"View Details\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleEditEquipment(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Edit Equipment\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleScheduleMaintenance(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Schedule Maintenance\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleTrackLocation(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Track Location\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleDeleteEquipment(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        className: \"text-red-600 focus:text-red-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 363,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Delete Equipment\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, equipment.id, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_EquipmentModal__WEBPACK_IMPORTED_MODULE_11__.EquipmentModal, {\n                isOpen: isEquipmentModalOpen,\n                onClose: ()=>setIsEquipmentModalOpen(false),\n                onSave: handleSaveEquipment,\n                equipment: selectedEquipment,\n                mode: modalMode\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_EquipmentDetailsModal__WEBPACK_IMPORTED_MODULE_12__.EquipmentDetailsModal, {\n                isOpen: isDetailsModalOpen,\n                onClose: ()=>setIsDetailsModalOpen(false),\n                equipment: selectedEquipment,\n                onEdit: handleEditEquipment,\n                onDelete: handleDeleteEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_DeleteConfirmDialog__WEBPACK_IMPORTED_MODULE_13__.DeleteConfirmDialog, {\n                isOpen: isDeleteDialogOpen,\n                onClose: ()=>setIsDeleteDialogOpen(false),\n                onConfirm: handleConfirmDelete,\n                equipment: selectedEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_equipment_location_modal__WEBPACK_IMPORTED_MODULE_14__.EquipmentLocationModal, {\n                isOpen: isLocationModalOpen,\n                onClose: ()=>setIsLocationModalOpen(false),\n                equipment: rawEquipment,\n                selectedEquipment: selectedEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(FleetOverviewPage, \"c8RWddfWYkkFPXLK8LvPJ0kfOzg=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast,\n        _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__.useEquipment\n    ];\n});\n_c = FleetOverviewPage;\nvar _c;\n$RefreshReg$(_c, \"FleetOverviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/fleet/page.tsx\n"));

/***/ })

});