"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/fleet/page",{

/***/ "(app-pages-browser)/./app/fleet/page.tsx":
/*!****************************!*\
  !*** ./app/fleet/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FleetOverviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useEquipment */ \"(app-pages-browser)/./lib/hooks/useEquipment.ts\");\n/* harmony import */ var _lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils/equipment */ \"(app-pages-browser)/./lib/utils/equipment.ts\");\n/* harmony import */ var _components_fleet_EquipmentModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/fleet/EquipmentModal */ \"(app-pages-browser)/./components/fleet/EquipmentModal.tsx\");\n/* harmony import */ var _components_fleet_EquipmentDetailsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/fleet/EquipmentDetailsModal */ \"(app-pages-browser)/./components/fleet/EquipmentDetailsModal.tsx\");\n/* harmony import */ var _components_fleet_DeleteConfirmDialog__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/fleet/DeleteConfirmDialog */ \"(app-pages-browser)/./components/fleet/DeleteConfirmDialog.tsx\");\n/* harmony import */ var _components_ui_equipment_location_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/equipment-location-modal */ \"(app-pages-browser)/./components/ui/equipment-location-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Component now uses real Firestore data via useEquipment hook\nfunction FleetOverviewPage() {\n    _s();\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sourceFilter, setSourceFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Modal states\n    const [isEquipmentModalOpen, setIsEquipmentModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDetailsModalOpen, setIsDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLocationModalOpen, setIsLocationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEquipment, setSelectedEquipment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modalMode, setModalMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('create');\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    // Use the custom hook to fetch equipment data\n    const { equipment: rawEquipment, loading, error, refetch, createEquipment, updateEquipment, deleteEquipment } = (0,_lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__.useEquipment)({\n        organizationId: 'demo-org',\n        autoRefresh: true,\n        refreshInterval: 30000 // Refresh every 30 seconds\n    });\n    // Transform Firestore data for UI display\n    const equipmentData = rawEquipment.map(_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.transformEquipmentForUI);\n    // Apply filters\n    const filteredEquipment = (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.filterEquipment)(equipmentData, {\n        status: statusFilter === \"all\" ? undefined : statusFilter,\n        source: sourceFilter === \"all\" ? undefined : sourceFilter,\n        search: searchTerm || undefined\n    });\n    // Get equipment statistics\n    const stats = (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getEquipmentStats)(equipmentData);\n    // CRUD handlers\n    const handleAddEquipment = ()=>{\n        setSelectedEquipment(null);\n        setModalMode('create');\n        setIsEquipmentModalOpen(true);\n    };\n    const handleEditEquipment = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setModalMode('edit');\n        setIsEquipmentModalOpen(true);\n    };\n    const handleViewDetails = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsDetailsModalOpen(true);\n    };\n    const handleDeleteEquipment = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsDeleteDialogOpen(true);\n    };\n    const handleSaveEquipment = async (data)=>{\n        try {\n            if (modalMode === 'create') {\n                await createEquipment(data);\n                toast({\n                    title: \"Equipment Added\",\n                    description: \"\".concat(data.name, \" has been successfully added to your fleet.\")\n                });\n            } else if (selectedEquipment) {\n                await updateEquipment(selectedEquipment.id, data);\n                toast({\n                    title: \"Equipment Updated\",\n                    description: \"\".concat(data.name, \" has been successfully updated.\")\n                });\n            }\n            setIsEquipmentModalOpen(false);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save equipment. Please try again.\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const handleConfirmDelete = async ()=>{\n        if (!selectedEquipment) return;\n        try {\n            await deleteEquipment(selectedEquipment.id);\n            toast({\n                title: \"Equipment Deleted\",\n                description: \"\".concat(selectedEquipment.name, \" has been removed from your fleet.\")\n            });\n            setIsDeleteDialogOpen(false);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete equipment. Please try again.\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const handleScheduleMaintenance = (equipment)=>{\n        // TODO: Implement maintenance scheduling\n        toast({\n            title: \"Feature Coming Soon\",\n            description: \"Maintenance scheduling will be available in a future update.\"\n        });\n    };\n    const handleTrackLocation = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsLocationModalOpen(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fleet Overview\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Manage and monitor your equipment fleet\",\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600 ml-2\",\n                                        children: \"• Loading...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 25\n                                    }, this),\n                                    !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600 ml-2\",\n                                        children: \"• Live from Firestore\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 26\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: refetch,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleAddEquipment,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Equipment\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-red-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Error loading equipment: \",\n                                    error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: refetch,\n                                className: \"ml-auto\",\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"Search equipment...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: statusFilter,\n                                onValueChange: setStatusFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-full md:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"active\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"maintenance\",\n                                                children: \"Maintenance\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"inactive\",\n                                                children: \"Inactive\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: sourceFilter,\n                                onValueChange: setSourceFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-full md:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Source\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Sources\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"samsara\",\n                                                children: \"Samsara\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"manual\",\n                                                children: \"Manual\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Equipment (\",\n                                        filteredEquipment.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Active (\",\n                                                stats.active,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Maintenance (\",\n                                                stats.maintenance,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Inactive (\",\n                                                stats.inactive,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400 ml-2\",\n                                            children: [\n                                                \"Utilization: \",\n                                                stats.utilization,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6 animate-spin text-gray-400 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Loading equipment...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this) : filteredEquipment.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: equipmentData.length === 0 ? \"No equipment found. Try adding some equipment or seeding demo data.\" : \"No equipment matches your current filters.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this),\n                                equipmentData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"mt-4\",\n                                    onClick: ()=>window.open('/firestore-demo', '_blank'),\n                                    children: \"Seed Demo Data\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Equipment Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Location\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Operator\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Last Update\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Source\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                    children: filteredEquipment.map((equipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                            className: \"hover:bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    className: \"font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: equipment.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.make && equipment.model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    equipment.make,\n                                                                    \" \",\n                                                                    equipment.model,\n                                                                    \" \",\n                                                                    equipment.year\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: equipment.type\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat((0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusDot)(equipment.status))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusColor)(equipment.status),\n                                                                children: equipment.status.charAt(0).toUpperCase() + equipment.status.slice(1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.location\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: equipment.operator\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-3 w-3 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.lastUpdate\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: equipment.source\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                                                align: \"end\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleViewDetails(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 326,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"View Details\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleEditEquipment(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 330,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Edit Equipment\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleScheduleMaintenance(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 335,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Schedule Maintenance\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleTrackLocation(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Track Location\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleDeleteEquipment(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        className: \"text-red-600 focus:text-red-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Delete Equipment\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, equipment.id, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_EquipmentModal__WEBPACK_IMPORTED_MODULE_11__.EquipmentModal, {\n                isOpen: isEquipmentModalOpen,\n                onClose: ()=>setIsEquipmentModalOpen(false),\n                onSave: handleSaveEquipment,\n                equipment: selectedEquipment,\n                mode: modalMode\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_EquipmentDetailsModal__WEBPACK_IMPORTED_MODULE_12__.EquipmentDetailsModal, {\n                isOpen: isDetailsModalOpen,\n                onClose: ()=>setIsDetailsModalOpen(false),\n                equipment: selectedEquipment,\n                onEdit: handleEditEquipment,\n                onDelete: handleDeleteEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_DeleteConfirmDialog__WEBPACK_IMPORTED_MODULE_13__.DeleteConfirmDialog, {\n                isOpen: isDeleteDialogOpen,\n                onClose: ()=>setIsDeleteDialogOpen(false),\n                onConfirm: handleConfirmDelete,\n                equipment: selectedEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_equipment_location_modal__WEBPACK_IMPORTED_MODULE_14__.EquipmentLocationModal, {\n                isOpen: isLocationModalOpen,\n                onClose: ()=>setIsLocationModalOpen(false),\n                equipment: rawEquipment,\n                selectedEquipment: selectedEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 385,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n_s(FleetOverviewPage, \"c8RWddfWYkkFPXLK8LvPJ0kfOzg=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast,\n        _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__.useEquipment\n    ];\n});\n_c = FleetOverviewPage;\nvar _c;\n$RefreshReg$(_c, \"FleetOverviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9mbGVldC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWdDO0FBQytDO0FBQ2hDO0FBQ0Y7QUFDQTtBQUN5RDtBQUNEO0FBQ2tDO0FBQ1E7QUFDeEY7QUFDZ0c7QUFDckY7QUFDYztBQUNKO0FBQ0s7QUFFckM7QUFFNUMsK0RBQStEO0FBRWhELFNBQVM4Qzs7SUFDdEIsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR2hELCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2lELGNBQWNDLGdCQUFnQixHQUFHbEQsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDbUQsWUFBWUMsY0FBYyxHQUFHcEQsK0NBQVFBLENBQUM7SUFFN0MsZUFBZTtJQUNmLE1BQU0sQ0FBQ3FELHNCQUFzQkMsd0JBQXdCLEdBQUd0RCwrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUN1RCxvQkFBb0JDLHNCQUFzQixHQUFHeEQsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDeUQsb0JBQW9CQyxzQkFBc0IsR0FBRzFELCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQzJELHFCQUFxQkMsdUJBQXVCLEdBQUc1RCwrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUM2RCxtQkFBbUJDLHFCQUFxQixHQUFHOUQsK0NBQVFBLENBQW1CO0lBQzdFLE1BQU0sQ0FBQytELFdBQVdDLGFBQWEsR0FBR2hFLCtDQUFRQSxDQUFvQjtJQUU5RCxNQUFNLEVBQUVpRSxLQUFLLEVBQUUsR0FBR3BCLDJEQUFRQTtJQUUxQiw4Q0FBOEM7SUFDOUMsTUFBTSxFQUFFcUIsV0FBV0MsWUFBWSxFQUFFQyxPQUFPLEVBQUVDLEtBQUssRUFBRUMsT0FBTyxFQUFFQyxlQUFlLEVBQUVDLGVBQWUsRUFBRUMsZUFBZSxFQUFFLEdBQUd0QyxxRUFBWUEsQ0FBQztRQUMzSHVDLGdCQUFnQjtRQUNoQkMsYUFBYTtRQUNiQyxpQkFBaUIsTUFBTSwyQkFBMkI7SUFDcEQ7SUFFQSwwQ0FBMEM7SUFDMUMsTUFBTUMsZ0JBQWdCVixhQUFhVyxHQUFHLENBQUMxQywwRUFBdUJBO0lBRTlELGdCQUFnQjtJQUNoQixNQUFNMkMsb0JBQW9CMUMsc0VBQWVBLENBQUN3QyxlQUFlO1FBQ3ZERyxRQUFRakMsaUJBQWlCLFFBQVFrQyxZQUFZbEM7UUFDN0NtQyxRQUFRakMsaUJBQWlCLFFBQVFnQyxZQUFZaEM7UUFDN0NrQyxRQUFRaEMsY0FBYzhCO0lBQ3hCO0lBRUEsMkJBQTJCO0lBQzNCLE1BQU1HLFFBQVE5Qyx3RUFBaUJBLENBQUN1QztJQUVoQyxnQkFBZ0I7SUFDaEIsTUFBTVEscUJBQXFCO1FBQ3pCdkIscUJBQXFCO1FBQ3JCRSxhQUFhO1FBQ2JWLHdCQUF3QjtJQUMxQjtJQUVBLE1BQU1nQyxzQkFBc0IsQ0FBQ3BCO1FBQzNCSixxQkFBcUJJO1FBQ3JCRixhQUFhO1FBQ2JWLHdCQUF3QjtJQUMxQjtJQUVBLE1BQU1pQyxvQkFBb0IsQ0FBQ3JCO1FBQ3pCSixxQkFBcUJJO1FBQ3JCVixzQkFBc0I7SUFDeEI7SUFFQSxNQUFNZ0Msd0JBQXdCLENBQUN0QjtRQUM3QkoscUJBQXFCSTtRQUNyQlIsc0JBQXNCO0lBQ3hCO0lBRUEsTUFBTStCLHNCQUFzQixPQUFPQztRQUNqQyxJQUFJO1lBQ0YsSUFBSTNCLGNBQWMsVUFBVTtnQkFDMUIsTUFBTVEsZ0JBQWdCbUI7Z0JBQ3RCekIsTUFBTTtvQkFDSjBCLE9BQU87b0JBQ1BDLGFBQWEsR0FBYSxPQUFWRixLQUFLRyxJQUFJLEVBQUM7Z0JBQzVCO1lBQ0YsT0FBTyxJQUFJaEMsbUJBQW1CO2dCQUM1QixNQUFNVyxnQkFBZ0JYLGtCQUFrQmlDLEVBQUUsRUFBRUo7Z0JBQzVDekIsTUFBTTtvQkFDSjBCLE9BQU87b0JBQ1BDLGFBQWEsR0FBYSxPQUFWRixLQUFLRyxJQUFJLEVBQUM7Z0JBQzVCO1lBQ0Y7WUFDQXZDLHdCQUF3QjtRQUMxQixFQUFFLE9BQU9lLE9BQU87WUFDZEosTUFBTTtnQkFDSjBCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JHLFNBQVM7WUFDWDtZQUNBLE1BQU0xQjtRQUNSO0lBQ0Y7SUFFQSxNQUFNMkIsc0JBQXNCO1FBQzFCLElBQUksQ0FBQ25DLG1CQUFtQjtRQUV4QixJQUFJO1lBQ0YsTUFBTVksZ0JBQWdCWixrQkFBa0JpQyxFQUFFO1lBQzFDN0IsTUFBTTtnQkFDSjBCLE9BQU87Z0JBQ1BDLGFBQWEsR0FBMEIsT0FBdkIvQixrQkFBa0JnQyxJQUFJLEVBQUM7WUFDekM7WUFDQW5DLHNCQUFzQjtRQUN4QixFQUFFLE9BQU9XLE9BQU87WUFDZEosTUFBTTtnQkFDSjBCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JHLFNBQVM7WUFDWDtZQUNBLE1BQU0xQjtRQUNSO0lBQ0Y7SUFFQSxNQUFNNEIsNEJBQTRCLENBQUMvQjtRQUNqQyx5Q0FBeUM7UUFDekNELE1BQU07WUFDSjBCLE9BQU87WUFDUEMsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNTSxzQkFBc0IsQ0FBQ2hDO1FBQzNCSixxQkFBcUJJO1FBQ3JCTix1QkFBdUI7SUFDekI7SUFFQSxxQkFDRSw4REFBQ3VDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEOzswQ0FDQyw4REFBQ0U7Z0NBQUdELFdBQVU7MENBQW1DOzs7Ozs7MENBQ2pELDhEQUFDRTtnQ0FBRUYsV0FBVTs7b0NBQWdCO29DQUUxQmhDLHlCQUFXLDhEQUFDbUM7d0NBQUtILFdBQVU7a0RBQXFCOzs7Ozs7b0NBQ2hELENBQUNoQyx5QkFBVyw4REFBQ21DO3dDQUFLSCxXQUFVO2tEQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUd2RCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDL0YseURBQU1BO2dDQUFDMEYsU0FBUTtnQ0FBVVMsU0FBU2xDO2dDQUFTbUMsVUFBVXJDOztrREFDcEQsOERBQUMzQyxtS0FBU0E7d0NBQUMyRSxXQUFXLGdCQUE4QyxPQUE5QmhDLFVBQVUsaUJBQWlCOzs7Ozs7b0NBQVE7Ozs7Ozs7MENBRzNFLDhEQUFDL0QseURBQU1BO2dDQUFDbUcsU0FBU25COztrREFDZiw4REFBQ2xFLG1LQUFJQTt3Q0FBQ2lGLFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFPdEMvQix1QkFDQyw4REFBQ3BFLHFEQUFJQTtnQkFBQ21HLFdBQVU7MEJBQ2QsNEVBQUNsRyw0REFBV0E7b0JBQUNrRyxXQUFVOzhCQUNyQiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDMUUsbUtBQVdBO2dDQUFDMEUsV0FBVTs7Ozs7OzBDQUN2Qiw4REFBQ0c7O29DQUFLO29DQUEwQmxDOzs7Ozs7OzBDQUNoQyw4REFBQ2hFLHlEQUFNQTtnQ0FBQzBGLFNBQVE7Z0NBQVVXLE1BQUs7Z0NBQUtGLFNBQVNsQztnQ0FBUzhCLFdBQVU7MENBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU2xGLDhEQUFDbkcscURBQUlBOzBCQUNILDRFQUFDQyw0REFBV0E7b0JBQUNrRyxXQUFVOzhCQUNyQiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNoRixtS0FBTUE7d0NBQUNnRixXQUFVOzs7Ozs7a0RBQ2xCLDhEQUFDN0YsdURBQUtBO3dDQUNKb0csYUFBWTt3Q0FDWkMsT0FBT3pEO3dDQUNQMEQsVUFBVSxDQUFDQyxJQUFNMUQsY0FBYzBELEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FDN0NSLFdBQVU7Ozs7Ozs7Ozs7OzswQ0FHZCw4REFBQzVGLHlEQUFNQTtnQ0FBQ29HLE9BQU83RDtnQ0FBY2lFLGVBQWVoRTs7a0RBQzFDLDhEQUFDckMsZ0VBQWFBO3dDQUFDeUYsV0FBVTtrREFDdkIsNEVBQUN4Riw4REFBV0E7NENBQUMrRixhQUFZOzs7Ozs7Ozs7OztrREFFM0IsOERBQUNsRyxnRUFBYUE7OzBEQUNaLDhEQUFDQyw2REFBVUE7Z0RBQUNrRyxPQUFNOzBEQUFNOzs7Ozs7MERBQ3hCLDhEQUFDbEcsNkRBQVVBO2dEQUFDa0csT0FBTTswREFBUzs7Ozs7OzBEQUMzQiw4REFBQ2xHLDZEQUFVQTtnREFBQ2tHLE9BQU07MERBQWM7Ozs7OzswREFDaEMsOERBQUNsRyw2REFBVUE7Z0RBQUNrRyxPQUFNOzBEQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBR2pDLDhEQUFDcEcseURBQU1BO2dDQUFDb0csT0FBTzNEO2dDQUFjK0QsZUFBZTlEOztrREFDMUMsOERBQUN2QyxnRUFBYUE7d0NBQUN5RixXQUFVO2tEQUN2Qiw0RUFBQ3hGLDhEQUFXQTs0Q0FBQytGLGFBQVk7Ozs7Ozs7Ozs7O2tEQUUzQiw4REFBQ2xHLGdFQUFhQTs7MERBQ1osOERBQUNDLDZEQUFVQTtnREFBQ2tHLE9BQU07MERBQU07Ozs7OzswREFDeEIsOERBQUNsRyw2REFBVUE7Z0RBQUNrRyxPQUFNOzBEQUFVOzs7Ozs7MERBQzVCLDhEQUFDbEcsNkRBQVVBO2dEQUFDa0csT0FBTTswREFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRckMsOERBQUMzRyxxREFBSUE7O2tDQUNILDhEQUFDRSwyREFBVUE7a0NBQ1QsNEVBQUNDLDBEQUFTQTs0QkFBQ2dHLFdBQVU7OzhDQUNuQiw4REFBQ0c7O3dDQUFLO3dDQUFZeEIsa0JBQWtCa0MsTUFBTTt3Q0FBQzs7Ozs7Ozs4Q0FDM0MsOERBQUNkO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7Ozs7O2dEQUEwQztnREFDaERoQixNQUFNOEIsTUFBTTtnREFBQzs7Ozs7OztzREFFeEIsOERBQUNmOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7Ozs7OztnREFBMkM7Z0RBQzVDaEIsTUFBTStCLFdBQVc7Z0RBQUM7Ozs7Ozs7c0RBRWxDLDhEQUFDaEI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7Ozs7O2dEQUF3QztnREFDNUNoQixNQUFNZ0MsUUFBUTtnREFBQzs7Ozs7OztzREFFNUIsOERBQUNqQjs0Q0FBSUMsV0FBVTs7Z0RBQTZCO2dEQUM1QmhCLE1BQU1pQyxXQUFXO2dEQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS3hDLDhEQUFDbkgsNERBQVdBO2tDQUNUa0Usd0JBQ0MsOERBQUMrQjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUMzRSxtS0FBU0E7b0NBQUMyRSxXQUFVOzs7Ozs7OENBQ3JCLDhEQUFDRztvQ0FBS0gsV0FBVTs4Q0FBZ0I7Ozs7Ozs7Ozs7O21DQUVoQ3JCLGtCQUFrQmtDLE1BQU0sS0FBSyxrQkFDL0IsOERBQUNkOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0U7b0NBQUVGLFdBQVU7OENBQ1Z2QixjQUFjb0MsTUFBTSxLQUFLLElBQ3RCLHdFQUNBOzs7Ozs7Z0NBR0xwQyxjQUFjb0MsTUFBTSxLQUFLLG1CQUN4Qiw4REFBQzVHLHlEQUFNQTtvQ0FDTDBGLFNBQVE7b0NBQ1JLLFdBQVU7b0NBQ1ZJLFNBQVMsSUFBTWMsT0FBT0MsSUFBSSxDQUFDLG1CQUFtQjs4Q0FDL0M7Ozs7Ozs7Ozs7O2lEQU1MLDhEQUFDMUcsdURBQUtBOzs4Q0FDSiw4REFBQ0ksNkRBQVdBOzhDQUNWLDRFQUFDQywwREFBUUE7OzBEQUNQLDhEQUFDRiwyREFBU0E7MERBQUM7Ozs7OzswREFDWCw4REFBQ0EsMkRBQVNBOzBEQUFDOzs7Ozs7MERBQ1gsOERBQUNBLDJEQUFTQTswREFBQzs7Ozs7OzBEQUNYLDhEQUFDQSwyREFBU0E7MERBQUM7Ozs7OzswREFDWCw4REFBQ0EsMkRBQVNBOzBEQUFDOzs7Ozs7MERBQ1gsOERBQUNBLDJEQUFTQTswREFBQzs7Ozs7OzBEQUNYLDhEQUFDQSwyREFBU0E7MERBQUM7Ozs7OzswREFDWCw4REFBQ0EsMkRBQVNBO2dEQUFDb0YsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR3pCLDhEQUFDdEYsMkRBQVNBOzhDQUNQaUUsa0JBQWtCRCxHQUFHLENBQUMsQ0FBQ1osMEJBQ3RCLDhEQUFDaEQsMERBQVFBOzRDQUFvQmtGLFdBQVU7OzhEQUNyQyw4REFBQ3JGLDJEQUFTQTtvREFBQ3FGLFdBQVU7OERBQ25CLDRFQUFDRDs7MEVBQ0MsOERBQUNBOzBFQUFLakMsVUFBVTJCLElBQUk7Ozs7Ozs0REFDbkIzQixVQUFVc0QsSUFBSSxJQUFJdEQsVUFBVXVELEtBQUssa0JBQ2hDLDhEQUFDdEI7Z0VBQUlDLFdBQVU7O29FQUNabEMsVUFBVXNELElBQUk7b0VBQUM7b0VBQUV0RCxVQUFVdUQsS0FBSztvRUFBQztvRUFBRXZELFVBQVV3RCxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSzFELDhEQUFDM0csMkRBQVNBOzhEQUFFbUQsVUFBVXlELElBQUk7Ozs7Ozs4REFDMUIsOERBQUM1RywyREFBU0E7OERBQ1IsNEVBQUNvRjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFXLHdCQUF1RCxPQUEvQjVELG1FQUFZQSxDQUFDMEIsVUFBVWMsTUFBTTs7Ozs7OzBFQUNyRSw4REFBQzFFLHVEQUFLQTtnRUFBQ3lGLFNBQVE7Z0VBQVlLLFdBQVc3RCxxRUFBY0EsQ0FBQzJCLFVBQVVjLE1BQU07MEVBQ2xFZCxVQUFVYyxNQUFNLENBQUM0QyxNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLM0QsVUFBVWMsTUFBTSxDQUFDOEMsS0FBSyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFJekUsOERBQUMvRywyREFBU0E7OERBQ1IsNEVBQUNvRjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUM5RSxtS0FBTUE7Z0VBQUM4RSxXQUFVOzs7Ozs7NERBQ2pCbEMsVUFBVTZELFFBQVE7Ozs7Ozs7Ozs7Ozs4REFHdkIsOERBQUNoSCwyREFBU0E7OERBQUVtRCxVQUFVOEQsUUFBUTs7Ozs7OzhEQUM5Qiw4REFBQ2pILDJEQUFTQTs4REFDUiw0RUFBQ29GO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQzdFLG1LQUFLQTtnRUFBQzZFLFdBQVU7Ozs7Ozs0REFDaEJsQyxVQUFVK0QsVUFBVTs7Ozs7Ozs7Ozs7OzhEQUd6Qiw4REFBQ2xILDJEQUFTQTs4REFDUiw0RUFBQ1QsdURBQUtBO3dEQUFDeUYsU0FBUTtrRUFBVzdCLFVBQVVnQixNQUFNOzs7Ozs7Ozs7Ozs4REFFNUMsOERBQUNuRSwyREFBU0E7OERBQ1IsNEVBQUNlLHNFQUFZQTs7MEVBQ1gsOERBQUNHLDZFQUFtQkE7Z0VBQUNpRyxPQUFPOzBFQUMxQiw0RUFBQzdILHlEQUFNQTtvRUFBQzBGLFNBQVE7b0VBQVFXLE1BQUs7OEVBQzNCLDRFQUFDckYsbUtBQWNBO3dFQUFDK0UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OzswRUFHOUIsOERBQUNyRSw2RUFBbUJBO2dFQUFDb0csT0FBTTs7a0ZBQ3pCLDhEQUFDbkcsMEVBQWdCQTt3RUFBQ3dFLFNBQVMsSUFBTWpCLGtCQUFrQnBCLGFBQWFpRSxJQUFJLENBQUN0QixDQUFBQSxJQUFLQSxFQUFFaEIsRUFBRSxLQUFLNUIsVUFBVTRCLEVBQUU7OzBGQUM3Riw4REFBQ3RFLG1LQUFHQTtnRkFBQzRFLFdBQVU7Ozs7Ozs0RUFBaUI7Ozs7Ozs7a0ZBR2xDLDhEQUFDcEUsMEVBQWdCQTt3RUFBQ3dFLFNBQVMsSUFBTWxCLG9CQUFvQm5CLGFBQWFpRSxJQUFJLENBQUN0QixDQUFBQSxJQUFLQSxFQUFFaEIsRUFBRSxLQUFLNUIsVUFBVTRCLEVBQUU7OzBGQUMvRiw4REFBQ25FLG1LQUFJQTtnRkFBQ3lFLFdBQVU7Ozs7Ozs0RUFBaUI7Ozs7Ozs7a0ZBR25DLDhEQUFDbEUsK0VBQXFCQTs7Ozs7a0ZBQ3RCLDhEQUFDRiwwRUFBZ0JBO3dFQUFDd0UsU0FBUyxJQUFNUCwwQkFBMEI5QixhQUFhaUUsSUFBSSxDQUFDdEIsQ0FBQUEsSUFBS0EsRUFBRWhCLEVBQUUsS0FBSzVCLFVBQVU0QixFQUFFOzswRkFDckcsOERBQUNqRSxtS0FBUUE7Z0ZBQUN1RSxXQUFVOzs7Ozs7NEVBQWlCOzs7Ozs7O2tGQUd2Qyw4REFBQ3BFLDBFQUFnQkE7d0VBQUN3RSxTQUFTLElBQU1OLG9CQUFvQi9CLGFBQWFpRSxJQUFJLENBQUN0QixDQUFBQSxJQUFLQSxFQUFFaEIsRUFBRSxLQUFLNUIsVUFBVTRCLEVBQUU7OzBGQUMvRiw4REFBQ3hFLG1LQUFNQTtnRkFBQzhFLFdBQVU7Ozs7Ozs0RUFBaUI7Ozs7Ozs7a0ZBR3JDLDhEQUFDbEUsK0VBQXFCQTs7Ozs7a0ZBQ3RCLDhEQUFDRiwwRUFBZ0JBO3dFQUNmd0UsU0FBUyxJQUFNaEIsc0JBQXNCckIsYUFBYWlFLElBQUksQ0FBQ3RCLENBQUFBLElBQUtBLEVBQUVoQixFQUFFLEtBQUs1QixVQUFVNEIsRUFBRTt3RUFDakZNLFdBQVU7OzBGQUVWLDhEQUFDeEUsbUtBQU1BO2dGQUFDd0UsV0FBVTs7Ozs7OzRFQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQ0FsRTlCbEMsVUFBVTRCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFpRnZDLDhEQUFDckQsNkVBQWNBO2dCQUNiNEYsUUFBUWhGO2dCQUNSaUYsU0FBUyxJQUFNaEYsd0JBQXdCO2dCQUN2Q2lGLFFBQVE5QztnQkFDUnZCLFdBQVdMO2dCQUNYMkUsTUFBTXpFOzs7Ozs7MEJBR1IsOERBQUNyQiwyRkFBcUJBO2dCQUNwQjJGLFFBQVE5RTtnQkFDUitFLFNBQVMsSUFBTTlFLHNCQUFzQjtnQkFDckNVLFdBQVdMO2dCQUNYNEUsUUFBUW5EO2dCQUNSb0QsVUFBVWxEOzs7Ozs7MEJBR1osOERBQUM3Qyx1RkFBbUJBO2dCQUNsQjBGLFFBQVE1RTtnQkFDUjZFLFNBQVMsSUFBTTVFLHNCQUFzQjtnQkFDckNpRixXQUFXM0M7Z0JBQ1g5QixXQUFXTDs7Ozs7OzBCQUdiLDhEQUFDakIsNEZBQXNCQTtnQkFDckJ5RixRQUFRMUU7Z0JBQ1IyRSxTQUFTLElBQU0xRSx1QkFBdUI7Z0JBQ3RDTSxXQUFXQztnQkFDWE4sbUJBQW1CQTs7Ozs7Ozs7Ozs7O0FBSTNCO0dBbFh3QmY7O1FBYUpELHVEQUFRQTtRQUdzRlYsaUVBQVlBOzs7S0FoQnRHVyIsInNvdXJjZXMiOlsiL1VzZXJzL2NncmFudC9kZXYvZGlnZ2l0LXdlYi9hcHAvZmxlZXQvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2lucHV0XCJcbmltcG9ydCB7IFNlbGVjdCwgU2VsZWN0Q29udGVudCwgU2VsZWN0SXRlbSwgU2VsZWN0VHJpZ2dlciwgU2VsZWN0VmFsdWUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NlbGVjdFwiXG5pbXBvcnQgeyBUYWJsZSwgVGFibGVCb2R5LCBUYWJsZUNlbGwsIFRhYmxlSGVhZCwgVGFibGVIZWFkZXIsIFRhYmxlUm93IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90YWJsZVwiXG5pbXBvcnQgeyBQbHVzLCBTZWFyY2gsIE1vcmVIb3Jpem9udGFsLCBNYXBQaW4sIENsb2NrLCBXcmVuY2gsIEV5ZSwgUmVmcmVzaEN3LCBBbGVydENpcmNsZSwgRWRpdCwgVHJhc2gyLCBDYWxlbmRhciB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHsgRHJvcGRvd25NZW51LCBEcm9wZG93bk1lbnVDb250ZW50LCBEcm9wZG93bk1lbnVJdGVtLCBEcm9wZG93bk1lbnVUcmlnZ2VyLCBEcm9wZG93bk1lbnVTZXBhcmF0b3IgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2Ryb3Bkb3duLW1lbnVcIlxuaW1wb3J0IHsgdXNlRXF1aXBtZW50IH0gZnJvbSBcIkAvbGliL2hvb2tzL3VzZUVxdWlwbWVudFwiXG5pbXBvcnQgeyB0cmFuc2Zvcm1FcXVpcG1lbnRGb3JVSSwgZmlsdGVyRXF1aXBtZW50LCBnZXRFcXVpcG1lbnRTdGF0cywgZ2V0U3RhdHVzQ29sb3IsIGdldFN0YXR1c0RvdCwgZ2V0U3RhdHVzRGlzcGxheVRleHQgfSBmcm9tIFwiQC9saWIvdXRpbHMvZXF1aXBtZW50XCJcbmltcG9ydCB7IEVxdWlwbWVudE1vZGFsIH0gZnJvbSBcIkAvY29tcG9uZW50cy9mbGVldC9FcXVpcG1lbnRNb2RhbFwiXG5pbXBvcnQgeyBFcXVpcG1lbnREZXRhaWxzTW9kYWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL2ZsZWV0L0VxdWlwbWVudERldGFpbHNNb2RhbFwiXG5pbXBvcnQgeyBEZWxldGVDb25maXJtRGlhbG9nIH0gZnJvbSBcIkAvY29tcG9uZW50cy9mbGVldC9EZWxldGVDb25maXJtRGlhbG9nXCJcbmltcG9ydCB7IEVxdWlwbWVudExvY2F0aW9uTW9kYWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2VxdWlwbWVudC1sb2NhdGlvbi1tb2RhbFwiXG5pbXBvcnQgeyBFcXVpcG1lbnQgfSBmcm9tIFwiQC9saWIvdHlwZXMvZmlyZXN0b3JlXCJcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSBcIkAvaG9va3MvdXNlLXRvYXN0XCJcblxuLy8gQ29tcG9uZW50IG5vdyB1c2VzIHJlYWwgRmlyZXN0b3JlIGRhdGEgdmlhIHVzZUVxdWlwbWVudCBob29rXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEZsZWV0T3ZlcnZpZXdQYWdlKCkge1xuICBjb25zdCBbc3RhdHVzRmlsdGVyLCBzZXRTdGF0dXNGaWx0ZXJdID0gdXNlU3RhdGUoXCJhbGxcIilcbiAgY29uc3QgW3NvdXJjZUZpbHRlciwgc2V0U291cmNlRmlsdGVyXSA9IHVzZVN0YXRlKFwiYWxsXCIpXG4gIGNvbnN0IFtzZWFyY2hUZXJtLCBzZXRTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKFwiXCIpXG5cbiAgLy8gTW9kYWwgc3RhdGVzXG4gIGNvbnN0IFtpc0VxdWlwbWVudE1vZGFsT3Blbiwgc2V0SXNFcXVpcG1lbnRNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtpc0RldGFpbHNNb2RhbE9wZW4sIHNldElzRGV0YWlsc01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2lzRGVsZXRlRGlhbG9nT3Blbiwgc2V0SXNEZWxldGVEaWFsb2dPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbaXNMb2NhdGlvbk1vZGFsT3Blbiwgc2V0SXNMb2NhdGlvbk1vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3NlbGVjdGVkRXF1aXBtZW50LCBzZXRTZWxlY3RlZEVxdWlwbWVudF0gPSB1c2VTdGF0ZTxFcXVpcG1lbnQgfCBudWxsPihudWxsKVxuICBjb25zdCBbbW9kYWxNb2RlLCBzZXRNb2RhbE1vZGVdID0gdXNlU3RhdGU8J2NyZWF0ZScgfCAnZWRpdCc+KCdjcmVhdGUnKVxuXG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KClcblxuICAvLyBVc2UgdGhlIGN1c3RvbSBob29rIHRvIGZldGNoIGVxdWlwbWVudCBkYXRhXG4gIGNvbnN0IHsgZXF1aXBtZW50OiByYXdFcXVpcG1lbnQsIGxvYWRpbmcsIGVycm9yLCByZWZldGNoLCBjcmVhdGVFcXVpcG1lbnQsIHVwZGF0ZUVxdWlwbWVudCwgZGVsZXRlRXF1aXBtZW50IH0gPSB1c2VFcXVpcG1lbnQoe1xuICAgIG9yZ2FuaXphdGlvbklkOiAnZGVtby1vcmcnLFxuICAgIGF1dG9SZWZyZXNoOiB0cnVlLFxuICAgIHJlZnJlc2hJbnRlcnZhbDogMzAwMDAgLy8gUmVmcmVzaCBldmVyeSAzMCBzZWNvbmRzXG4gIH0pXG5cbiAgLy8gVHJhbnNmb3JtIEZpcmVzdG9yZSBkYXRhIGZvciBVSSBkaXNwbGF5XG4gIGNvbnN0IGVxdWlwbWVudERhdGEgPSByYXdFcXVpcG1lbnQubWFwKHRyYW5zZm9ybUVxdWlwbWVudEZvclVJKVxuXG4gIC8vIEFwcGx5IGZpbHRlcnNcbiAgY29uc3QgZmlsdGVyZWRFcXVpcG1lbnQgPSBmaWx0ZXJFcXVpcG1lbnQoZXF1aXBtZW50RGF0YSwge1xuICAgIHN0YXR1czogc3RhdHVzRmlsdGVyID09PSBcImFsbFwiID8gdW5kZWZpbmVkIDogc3RhdHVzRmlsdGVyLFxuICAgIHNvdXJjZTogc291cmNlRmlsdGVyID09PSBcImFsbFwiID8gdW5kZWZpbmVkIDogc291cmNlRmlsdGVyLFxuICAgIHNlYXJjaDogc2VhcmNoVGVybSB8fCB1bmRlZmluZWRcbiAgfSlcblxuICAvLyBHZXQgZXF1aXBtZW50IHN0YXRpc3RpY3NcbiAgY29uc3Qgc3RhdHMgPSBnZXRFcXVpcG1lbnRTdGF0cyhlcXVpcG1lbnREYXRhKVxuXG4gIC8vIENSVUQgaGFuZGxlcnNcbiAgY29uc3QgaGFuZGxlQWRkRXF1aXBtZW50ID0gKCkgPT4ge1xuICAgIHNldFNlbGVjdGVkRXF1aXBtZW50KG51bGwpXG4gICAgc2V0TW9kYWxNb2RlKCdjcmVhdGUnKVxuICAgIHNldElzRXF1aXBtZW50TW9kYWxPcGVuKHRydWUpXG4gIH1cblxuICBjb25zdCBoYW5kbGVFZGl0RXF1aXBtZW50ID0gKGVxdWlwbWVudDogRXF1aXBtZW50KSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRFcXVpcG1lbnQoZXF1aXBtZW50KVxuICAgIHNldE1vZGFsTW9kZSgnZWRpdCcpXG4gICAgc2V0SXNFcXVpcG1lbnRNb2RhbE9wZW4odHJ1ZSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVZpZXdEZXRhaWxzID0gKGVxdWlwbWVudDogRXF1aXBtZW50KSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRFcXVpcG1lbnQoZXF1aXBtZW50KVxuICAgIHNldElzRGV0YWlsc01vZGFsT3Blbih0cnVlKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlRGVsZXRlRXF1aXBtZW50ID0gKGVxdWlwbWVudDogRXF1aXBtZW50KSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRFcXVpcG1lbnQoZXF1aXBtZW50KVxuICAgIHNldElzRGVsZXRlRGlhbG9nT3Blbih0cnVlKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU2F2ZUVxdWlwbWVudCA9IGFzeW5jIChkYXRhOiBQYXJ0aWFsPEVxdWlwbWVudD4pID0+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKG1vZGFsTW9kZSA9PT0gJ2NyZWF0ZScpIHtcbiAgICAgICAgYXdhaXQgY3JlYXRlRXF1aXBtZW50KGRhdGEpXG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogXCJFcXVpcG1lbnQgQWRkZWRcIixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogYCR7ZGF0YS5uYW1lfSBoYXMgYmVlbiBzdWNjZXNzZnVsbHkgYWRkZWQgdG8geW91ciBmbGVldC5gLFxuICAgICAgICB9KVxuICAgICAgfSBlbHNlIGlmIChzZWxlY3RlZEVxdWlwbWVudCkge1xuICAgICAgICBhd2FpdCB1cGRhdGVFcXVpcG1lbnQoc2VsZWN0ZWRFcXVpcG1lbnQuaWQsIGRhdGEpXG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogXCJFcXVpcG1lbnQgVXBkYXRlZFwiLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBgJHtkYXRhLm5hbWV9IGhhcyBiZWVuIHN1Y2Nlc3NmdWxseSB1cGRhdGVkLmAsXG4gICAgICAgIH0pXG4gICAgICB9XG4gICAgICBzZXRJc0VxdWlwbWVudE1vZGFsT3BlbihmYWxzZSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJFcnJvclwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gc2F2ZSBlcXVpcG1lbnQuIFBsZWFzZSB0cnkgYWdhaW4uXCIsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcbiAgICAgIH0pXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUNvbmZpcm1EZWxldGUgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZEVxdWlwbWVudCkgcmV0dXJuXG5cbiAgICB0cnkge1xuICAgICAgYXdhaXQgZGVsZXRlRXF1aXBtZW50KHNlbGVjdGVkRXF1aXBtZW50LmlkKVxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJFcXVpcG1lbnQgRGVsZXRlZFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogYCR7c2VsZWN0ZWRFcXVpcG1lbnQubmFtZX0gaGFzIGJlZW4gcmVtb3ZlZCBmcm9tIHlvdXIgZmxlZXQuYCxcbiAgICAgIH0pXG4gICAgICBzZXRJc0RlbGV0ZURpYWxvZ09wZW4oZmFsc2UpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiRXJyb3JcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiRmFpbGVkIHRvIGRlbGV0ZSBlcXVpcG1lbnQuIFBsZWFzZSB0cnkgYWdhaW4uXCIsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcbiAgICAgIH0pXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVNjaGVkdWxlTWFpbnRlbmFuY2UgPSAoZXF1aXBtZW50OiBFcXVpcG1lbnQpID0+IHtcbiAgICAvLyBUT0RPOiBJbXBsZW1lbnQgbWFpbnRlbmFuY2Ugc2NoZWR1bGluZ1xuICAgIHRvYXN0KHtcbiAgICAgIHRpdGxlOiBcIkZlYXR1cmUgQ29taW5nIFNvb25cIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIk1haW50ZW5hbmNlIHNjaGVkdWxpbmcgd2lsbCBiZSBhdmFpbGFibGUgaW4gYSBmdXR1cmUgdXBkYXRlLlwiLFxuICAgIH0pXG4gIH1cblxuICBjb25zdCBoYW5kbGVUcmFja0xvY2F0aW9uID0gKGVxdWlwbWVudDogRXF1aXBtZW50KSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRFcXVpcG1lbnQoZXF1aXBtZW50KVxuICAgIHNldElzTG9jYXRpb25Nb2RhbE9wZW4odHJ1ZSlcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgc3BhY2UteS02XCI+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5GbGVldCBPdmVydmlldzwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgTWFuYWdlIGFuZCBtb25pdG9yIHlvdXIgZXF1aXBtZW50IGZsZWV0XG4gICAgICAgICAgICB7bG9hZGluZyAmJiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIG1sLTJcIj7igKIgTG9hZGluZy4uLjwvc3Bhbj59XG4gICAgICAgICAgICB7IWxvYWRpbmcgJiYgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDAgbWwtMlwiPuKAoiBMaXZlIGZyb20gRmlyZXN0b3JlPC9zcGFuPn1cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17cmVmZXRjaH0gZGlzYWJsZWQ9e2xvYWRpbmd9PlxuICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2BoLTQgdy00IG1yLTIgJHtsb2FkaW5nID8gJ2FuaW1hdGUtc3BpbicgOiAnJ31gfSAvPlxuICAgICAgICAgICAgUmVmcmVzaFxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlQWRkRXF1aXBtZW50fT5cbiAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICBBZGQgRXF1aXBtZW50XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBFcnJvciBTdGF0ZSAqL31cbiAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJvcmRlci1yZWQtMjAwIGJnLXJlZC01MFwiPlxuICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwdC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtcmVkLTgwMFwiPlxuICAgICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuPkVycm9yIGxvYWRpbmcgZXF1aXBtZW50OiB7ZXJyb3J9PC9zcGFuPlxuICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgb25DbGljaz17cmVmZXRjaH0gY2xhc3NOYW1lPVwibWwtYXV0b1wiPlxuICAgICAgICAgICAgICAgIFRyeSBBZ2FpblxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBGaWx0ZXJzICovfVxuICAgICAgPENhcmQ+XG4gICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwdC02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGdhcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXgtMVwiPlxuICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIGVxdWlwbWVudC4uLlwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFRlcm19XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hUZXJtKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0xMFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxTZWxlY3QgdmFsdWU9e3N0YXR1c0ZpbHRlcn0gb25WYWx1ZUNoYW5nZT17c2V0U3RhdHVzRmlsdGVyfT5cbiAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPVwidy1mdWxsIG1kOnctNDhcIj5cbiAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgcGxhY2Vob2xkZXI9XCJTdGF0dXNcIiAvPlxuICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiYWxsXCI+QWxsIFN0YXR1czwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImFjdGl2ZVwiPkFjdGl2ZTwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIm1haW50ZW5hbmNlXCI+TWFpbnRlbmFuY2U8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJpbmFjdGl2ZVwiPkluYWN0aXZlPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgIDxTZWxlY3QgdmFsdWU9e3NvdXJjZUZpbHRlcn0gb25WYWx1ZUNoYW5nZT17c2V0U291cmNlRmlsdGVyfT5cbiAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPVwidy1mdWxsIG1kOnctNDhcIj5cbiAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgcGxhY2Vob2xkZXI9XCJTb3VyY2VcIiAvPlxuICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiYWxsXCI+QWxsIFNvdXJjZXM8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJzYW1zYXJhXCI+U2Ftc2FyYTwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIm1hbnVhbFwiPk1hbnVhbDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIHsvKiBFcXVpcG1lbnQgVGFibGUgKi99XG4gICAgICA8Q2FyZD5cbiAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxzcGFuPkVxdWlwbWVudCAoe2ZpbHRlcmVkRXF1aXBtZW50Lmxlbmd0aH0pPC9zcGFuPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiByb3VuZGVkLWZ1bGwgYmctZ3JlZW4tNDAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgQWN0aXZlICh7c3RhdHMuYWN0aXZlfSlcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgcm91bmRlZC1mdWxsIGJnLXllbGxvdy00MDBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICBNYWludGVuYW5jZSAoe3N0YXRzLm1haW50ZW5hbmNlfSlcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgcm91bmRlZC1mdWxsIGJnLXJlZC00MDBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICBJbmFjdGl2ZSAoe3N0YXRzLmluYWN0aXZlfSlcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwIG1sLTJcIj5cbiAgICAgICAgICAgICAgICBVdGlsaXphdGlvbjoge3N0YXRzLnV0aWxpemF0aW9ufSVcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJoLTYgdy02IGFuaW1hdGUtc3BpbiB0ZXh0LWdyYXktNDAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+TG9hZGluZyBlcXVpcG1lbnQuLi48L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApIDogZmlsdGVyZWRFcXVpcG1lbnQubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICB7ZXF1aXBtZW50RGF0YS5sZW5ndGggPT09IDBcbiAgICAgICAgICAgICAgICAgID8gXCJObyBlcXVpcG1lbnQgZm91bmQuIFRyeSBhZGRpbmcgc29tZSBlcXVpcG1lbnQgb3Igc2VlZGluZyBkZW1vIGRhdGEuXCJcbiAgICAgICAgICAgICAgICAgIDogXCJObyBlcXVpcG1lbnQgbWF0Y2hlcyB5b3VyIGN1cnJlbnQgZmlsdGVycy5cIlxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICB7ZXF1aXBtZW50RGF0YS5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTRcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93Lm9wZW4oJy9maXJlc3RvcmUtZGVtbycsICdfYmxhbmsnKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBTZWVkIERlbW8gRGF0YVxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxUYWJsZT5cbiAgICAgICAgICAgICAgPFRhYmxlSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxUYWJsZVJvdz5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+RXF1aXBtZW50IE5hbWU8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+VHlwZTwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlSGVhZD5TdGF0dXM8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+TG9jYXRpb248L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+T3BlcmF0b3I8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+TGFzdCBVcGRhdGU8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+U291cmNlPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICA8VGFibGVIZWFkIGNsYXNzTmFtZT1cInctMTJcIj48L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgICA8L1RhYmxlSGVhZGVyPlxuICAgICAgICAgICAgICA8VGFibGVCb2R5PlxuICAgICAgICAgICAgICAgIHtmaWx0ZXJlZEVxdWlwbWVudC5tYXAoKGVxdWlwbWVudCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPFRhYmxlUm93IGtleT17ZXF1aXBtZW50LmlkfSBjbGFzc05hbWU9XCJob3ZlcjpiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj57ZXF1aXBtZW50Lm5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZXF1aXBtZW50Lm1ha2UgJiYgZXF1aXBtZW50Lm1vZGVsICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZXF1aXBtZW50Lm1ha2V9IHtlcXVpcG1lbnQubW9kZWx9IHtlcXVpcG1lbnQueWVhcn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD57ZXF1aXBtZW50LnR5cGV9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTIgaC0yIHJvdW5kZWQtZnVsbCAke2dldFN0YXR1c0RvdChlcXVpcG1lbnQuc3RhdHVzKX1gfT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwic2Vjb25kYXJ5XCIgY2xhc3NOYW1lPXtnZXRTdGF0dXNDb2xvcihlcXVpcG1lbnQuc3RhdHVzKX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtlcXVpcG1lbnQuc3RhdHVzLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgZXF1aXBtZW50LnN0YXR1cy5zbGljZSgxKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxNYXBQaW4gY2xhc3NOYW1lPVwiaC0zIHctMyB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtlcXVpcG1lbnQubG9jYXRpb259XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPntlcXVpcG1lbnQub3BlcmF0b3J9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtMyB3LTMgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZXF1aXBtZW50Lmxhc3RVcGRhdGV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiPntlcXVpcG1lbnQuc291cmNlfTwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51VHJpZ2dlciBhc0NoaWxkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJpY29uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1vcmVIb3Jpem9udGFsIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDb250ZW50IGFsaWduPVwiZW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtIG9uQ2xpY2s9eygpID0+IGhhbmRsZVZpZXdEZXRhaWxzKHJhd0VxdWlwbWVudC5maW5kKGUgPT4gZS5pZCA9PT0gZXF1aXBtZW50LmlkKSEpfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgVmlldyBEZXRhaWxzXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0gb25DbGljaz17KCkgPT4gaGFuZGxlRWRpdEVxdWlwbWVudChyYXdFcXVpcG1lbnQuZmluZChlID0+IGUuaWQgPT09IGVxdWlwbWVudC5pZCkhKX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVkaXQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBFZGl0IEVxdWlwbWVudFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVTZXBhcmF0b3IgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudUl0ZW0gb25DbGljaz17KCkgPT4gaGFuZGxlU2NoZWR1bGVNYWludGVuYW5jZShyYXdFcXVpcG1lbnQuZmluZChlID0+IGUuaWQgPT09IGVxdWlwbWVudC5pZCkhKX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgU2NoZWR1bGUgTWFpbnRlbmFuY2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51SXRlbSBvbkNsaWNrPXsoKSA9PiBoYW5kbGVUcmFja0xvY2F0aW9uKHJhd0VxdWlwbWVudC5maW5kKGUgPT4gZS5pZCA9PT0gZXF1aXBtZW50LmlkKSEpfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TWFwUGluIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgVHJhY2sgTG9jYXRpb25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8RHJvcGRvd25NZW51U2VwYXJhdG9yIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVJdGVtXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGVsZXRlRXF1aXBtZW50KHJhd0VxdWlwbWVudC5maW5kKGUgPT4gZS5pZCA9PT0gZXF1aXBtZW50LmlkKSEpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBmb2N1czp0ZXh0LXJlZC02MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyYXNoMiBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIERlbGV0ZSBFcXVpcG1lbnRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd25NZW51PlxuICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvVGFibGVCb2R5PlxuICAgICAgICAgICAgPC9UYWJsZT5cbiAgICAgICAgICApfVxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuXG4gICAgICB7LyogTW9kYWxzICovfVxuICAgICAgPEVxdWlwbWVudE1vZGFsXG4gICAgICAgIGlzT3Blbj17aXNFcXVpcG1lbnRNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzRXF1aXBtZW50TW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgb25TYXZlPXtoYW5kbGVTYXZlRXF1aXBtZW50fVxuICAgICAgICBlcXVpcG1lbnQ9e3NlbGVjdGVkRXF1aXBtZW50fVxuICAgICAgICBtb2RlPXttb2RhbE1vZGV9XG4gICAgICAvPlxuXG4gICAgICA8RXF1aXBtZW50RGV0YWlsc01vZGFsXG4gICAgICAgIGlzT3Blbj17aXNEZXRhaWxzTW9kYWxPcGVufVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRJc0RldGFpbHNNb2RhbE9wZW4oZmFsc2UpfVxuICAgICAgICBlcXVpcG1lbnQ9e3NlbGVjdGVkRXF1aXBtZW50fVxuICAgICAgICBvbkVkaXQ9e2hhbmRsZUVkaXRFcXVpcG1lbnR9XG4gICAgICAgIG9uRGVsZXRlPXtoYW5kbGVEZWxldGVFcXVpcG1lbnR9XG4gICAgICAvPlxuXG4gICAgICA8RGVsZXRlQ29uZmlybURpYWxvZ1xuICAgICAgICBpc09wZW49e2lzRGVsZXRlRGlhbG9nT3Blbn1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNEZWxldGVEaWFsb2dPcGVuKGZhbHNlKX1cbiAgICAgICAgb25Db25maXJtPXtoYW5kbGVDb25maXJtRGVsZXRlfVxuICAgICAgICBlcXVpcG1lbnQ9e3NlbGVjdGVkRXF1aXBtZW50fVxuICAgICAgLz5cblxuICAgICAgPEVxdWlwbWVudExvY2F0aW9uTW9kYWxcbiAgICAgICAgaXNPcGVuPXtpc0xvY2F0aW9uTW9kYWxPcGVufVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRJc0xvY2F0aW9uTW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgZXF1aXBtZW50PXtyYXdFcXVpcG1lbnR9XG4gICAgICAgIHNlbGVjdGVkRXF1aXBtZW50PXtzZWxlY3RlZEVxdWlwbWVudH1cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCdXR0b24iLCJCYWRnZSIsIklucHV0IiwiU2VsZWN0IiwiU2VsZWN0Q29udGVudCIsIlNlbGVjdEl0ZW0iLCJTZWxlY3RUcmlnZ2VyIiwiU2VsZWN0VmFsdWUiLCJUYWJsZSIsIlRhYmxlQm9keSIsIlRhYmxlQ2VsbCIsIlRhYmxlSGVhZCIsIlRhYmxlSGVhZGVyIiwiVGFibGVSb3ciLCJQbHVzIiwiU2VhcmNoIiwiTW9yZUhvcml6b250YWwiLCJNYXBQaW4iLCJDbG9jayIsIkV5ZSIsIlJlZnJlc2hDdyIsIkFsZXJ0Q2lyY2xlIiwiRWRpdCIsIlRyYXNoMiIsIkNhbGVuZGFyIiwiRHJvcGRvd25NZW51IiwiRHJvcGRvd25NZW51Q29udGVudCIsIkRyb3Bkb3duTWVudUl0ZW0iLCJEcm9wZG93bk1lbnVUcmlnZ2VyIiwiRHJvcGRvd25NZW51U2VwYXJhdG9yIiwidXNlRXF1aXBtZW50IiwidHJhbnNmb3JtRXF1aXBtZW50Rm9yVUkiLCJmaWx0ZXJFcXVpcG1lbnQiLCJnZXRFcXVpcG1lbnRTdGF0cyIsImdldFN0YXR1c0NvbG9yIiwiZ2V0U3RhdHVzRG90IiwiRXF1aXBtZW50TW9kYWwiLCJFcXVpcG1lbnREZXRhaWxzTW9kYWwiLCJEZWxldGVDb25maXJtRGlhbG9nIiwiRXF1aXBtZW50TG9jYXRpb25Nb2RhbCIsInVzZVRvYXN0IiwiRmxlZXRPdmVydmlld1BhZ2UiLCJzdGF0dXNGaWx0ZXIiLCJzZXRTdGF0dXNGaWx0ZXIiLCJzb3VyY2VGaWx0ZXIiLCJzZXRTb3VyY2VGaWx0ZXIiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsImlzRXF1aXBtZW50TW9kYWxPcGVuIiwic2V0SXNFcXVpcG1lbnRNb2RhbE9wZW4iLCJpc0RldGFpbHNNb2RhbE9wZW4iLCJzZXRJc0RldGFpbHNNb2RhbE9wZW4iLCJpc0RlbGV0ZURpYWxvZ09wZW4iLCJzZXRJc0RlbGV0ZURpYWxvZ09wZW4iLCJpc0xvY2F0aW9uTW9kYWxPcGVuIiwic2V0SXNMb2NhdGlvbk1vZGFsT3BlbiIsInNlbGVjdGVkRXF1aXBtZW50Iiwic2V0U2VsZWN0ZWRFcXVpcG1lbnQiLCJtb2RhbE1vZGUiLCJzZXRNb2RhbE1vZGUiLCJ0b2FzdCIsImVxdWlwbWVudCIsInJhd0VxdWlwbWVudCIsImxvYWRpbmciLCJlcnJvciIsInJlZmV0Y2giLCJjcmVhdGVFcXVpcG1lbnQiLCJ1cGRhdGVFcXVpcG1lbnQiLCJkZWxldGVFcXVpcG1lbnQiLCJvcmdhbml6YXRpb25JZCIsImF1dG9SZWZyZXNoIiwicmVmcmVzaEludGVydmFsIiwiZXF1aXBtZW50RGF0YSIsIm1hcCIsImZpbHRlcmVkRXF1aXBtZW50Iiwic3RhdHVzIiwidW5kZWZpbmVkIiwic291cmNlIiwic2VhcmNoIiwic3RhdHMiLCJoYW5kbGVBZGRFcXVpcG1lbnQiLCJoYW5kbGVFZGl0RXF1aXBtZW50IiwiaGFuZGxlVmlld0RldGFpbHMiLCJoYW5kbGVEZWxldGVFcXVpcG1lbnQiLCJoYW5kbGVTYXZlRXF1aXBtZW50IiwiZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJuYW1lIiwiaWQiLCJ2YXJpYW50IiwiaGFuZGxlQ29uZmlybURlbGV0ZSIsImhhbmRsZVNjaGVkdWxlTWFpbnRlbmFuY2UiLCJoYW5kbGVUcmFja0xvY2F0aW9uIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwic3BhbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInNpemUiLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwib25WYWx1ZUNoYW5nZSIsImxlbmd0aCIsImFjdGl2ZSIsIm1haW50ZW5hbmNlIiwiaW5hY3RpdmUiLCJ1dGlsaXphdGlvbiIsIndpbmRvdyIsIm9wZW4iLCJtYWtlIiwibW9kZWwiLCJ5ZWFyIiwidHlwZSIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJsb2NhdGlvbiIsIm9wZXJhdG9yIiwibGFzdFVwZGF0ZSIsImFzQ2hpbGQiLCJhbGlnbiIsImZpbmQiLCJpc09wZW4iLCJvbkNsb3NlIiwib25TYXZlIiwibW9kZSIsIm9uRWRpdCIsIm9uRGVsZXRlIiwib25Db25maXJtIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/fleet/page.tsx\n"));

/***/ })

});