"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/fleet/page",{

/***/ "(app-pages-browser)/./app/fleet/page.tsx":
/*!****************************!*\
  !*** ./app/fleet/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FleetOverviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useEquipment */ \"(app-pages-browser)/./lib/hooks/useEquipment.ts\");\n/* harmony import */ var _lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils/equipment */ \"(app-pages-browser)/./lib/utils/equipment.ts\");\n/* harmony import */ var _components_fleet_EquipmentModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/fleet/EquipmentModal */ \"(app-pages-browser)/./components/fleet/EquipmentModal.tsx\");\n/* harmony import */ var _components_fleet_EquipmentDetailsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/fleet/EquipmentDetailsModal */ \"(app-pages-browser)/./components/fleet/EquipmentDetailsModal.tsx\");\n/* harmony import */ var _components_fleet_DeleteConfirmDialog__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/fleet/DeleteConfirmDialog */ \"(app-pages-browser)/./components/fleet/DeleteConfirmDialog.tsx\");\n/* harmony import */ var _components_ui_equipment_location_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/equipment-location-modal */ \"(app-pages-browser)/./components/ui/equipment-location-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Component now uses real Firestore data via useEquipment hook\nfunction FleetOverviewPage() {\n    _s();\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sourceFilter, setSourceFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Modal states\n    const [isEquipmentModalOpen, setIsEquipmentModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDetailsModalOpen, setIsDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLocationModalOpen, setIsLocationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEquipment, setSelectedEquipment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modalMode, setModalMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('create');\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    // Use the custom hook to fetch equipment data\n    const { equipment: rawEquipment, loading, error, refetch, createEquipment, updateEquipment, deleteEquipment } = (0,_lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__.useEquipment)({\n        organizationId: 'demo-org',\n        autoRefresh: true,\n        refreshInterval: 30000 // Refresh every 30 seconds\n    });\n    // Transform Firestore data for UI display\n    const equipmentData = rawEquipment.map(_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.transformEquipmentForUI);\n    // Apply filters\n    const filteredEquipment = (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.filterEquipment)(equipmentData, {\n        status: statusFilter === \"all\" ? undefined : statusFilter,\n        source: sourceFilter === \"all\" ? undefined : sourceFilter,\n        search: searchTerm || undefined\n    });\n    // Get equipment statistics\n    const stats = (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getEquipmentStats)(equipmentData);\n    // Get status counts for filter dropdown\n    const getStatusCounts = ()=>{\n        const counts = {};\n        equipmentData.forEach((equipment)=>{\n            counts[equipment.status] = (counts[equipment.status] || 0) + 1;\n        });\n        return counts;\n    };\n    const statusCounts = getStatusCounts();\n    // CRUD handlers\n    const handleAddEquipment = ()=>{\n        setSelectedEquipment(null);\n        setModalMode('create');\n        setIsEquipmentModalOpen(true);\n    };\n    const handleEditEquipment = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setModalMode('edit');\n        setIsEquipmentModalOpen(true);\n    };\n    const handleViewDetails = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsDetailsModalOpen(true);\n    };\n    const handleDeleteEquipment = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsDeleteDialogOpen(true);\n    };\n    const handleSaveEquipment = async (data)=>{\n        try {\n            if (modalMode === 'create') {\n                await createEquipment(data);\n                toast({\n                    title: \"Equipment Added\",\n                    description: \"\".concat(data.name, \" has been successfully added to your fleet.\")\n                });\n            } else if (selectedEquipment) {\n                await updateEquipment(selectedEquipment.id, data);\n                toast({\n                    title: \"Equipment Updated\",\n                    description: \"\".concat(data.name, \" has been successfully updated.\")\n                });\n            }\n            setIsEquipmentModalOpen(false);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save equipment. Please try again.\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const handleConfirmDelete = async ()=>{\n        if (!selectedEquipment) return;\n        try {\n            await deleteEquipment(selectedEquipment.id);\n            toast({\n                title: \"Equipment Deleted\",\n                description: \"\".concat(selectedEquipment.name, \" has been removed from your fleet.\")\n            });\n            setIsDeleteDialogOpen(false);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete equipment. Please try again.\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const handleScheduleMaintenance = (equipment)=>{\n        // TODO: Implement maintenance scheduling\n        toast({\n            title: \"Feature Coming Soon\",\n            description: \"Maintenance scheduling will be available in a future update.\"\n        });\n    };\n    const handleTrackLocation = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsLocationModalOpen(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fleet Overview\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Manage and monitor your equipment fleet\",\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600 ml-2\",\n                                        children: \"• Loading...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 25\n                                    }, this),\n                                    !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600 ml-2\",\n                                        children: \"• Live from Firestore\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 26\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: refetch,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleAddEquipment,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Equipment\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-red-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Error loading equipment: \",\n                                    error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: refetch,\n                                className: \"ml-auto\",\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"Search equipment...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: statusFilter,\n                                onValueChange: setStatusFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-full md:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            _lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.equipmentStatusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: sourceFilter,\n                                onValueChange: setSourceFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-full md:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Source\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Sources\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"samsara\",\n                                                children: \"Samsara\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"manual\",\n                                                children: \"Manual\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Equipment (\",\n                                        filteredEquipment.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Active (\",\n                                                stats.active,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Maintenance (\",\n                                                stats.maintenance,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Inactive (\",\n                                                stats.inactive,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400 ml-2\",\n                                            children: [\n                                                \"Utilization: \",\n                                                stats.utilization,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6 animate-spin text-gray-400 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Loading equipment...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this) : filteredEquipment.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: equipmentData.length === 0 ? \"No equipment found. Try adding some equipment or seeding demo data.\" : \"No equipment matches your current filters.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this),\n                                equipmentData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"mt-4\",\n                                    onClick: ()=>window.open('/firestore-demo', '_blank'),\n                                    children: \"Seed Demo Data\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Equipment Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Location\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Operator\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Last Update\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Source\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                    children: filteredEquipment.map((equipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                            className: \"hover:bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    className: \"font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: equipment.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.make && equipment.model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    equipment.make,\n                                                                    \" \",\n                                                                    equipment.model,\n                                                                    \" \",\n                                                                    equipment.year\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: equipment.type\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat((0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusDot)(equipment.status))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusColor)(equipment.status),\n                                                                children: (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusDisplayText)(equipment.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.location\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: equipment.operator\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-3 w-3 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.lastUpdate\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: equipment.source\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                                                align: \"end\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleViewDetails(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"View Details\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleEditEquipment(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Edit Equipment\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleScheduleMaintenance(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 348,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Schedule Maintenance\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleTrackLocation(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Track Location\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleDeleteEquipment(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        className: \"text-red-600 focus:text-red-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Delete Equipment\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, equipment.id, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_EquipmentModal__WEBPACK_IMPORTED_MODULE_11__.EquipmentModal, {\n                isOpen: isEquipmentModalOpen,\n                onClose: ()=>setIsEquipmentModalOpen(false),\n                onSave: handleSaveEquipment,\n                equipment: selectedEquipment,\n                mode: modalMode\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_EquipmentDetailsModal__WEBPACK_IMPORTED_MODULE_12__.EquipmentDetailsModal, {\n                isOpen: isDetailsModalOpen,\n                onClose: ()=>setIsDetailsModalOpen(false),\n                equipment: selectedEquipment,\n                onEdit: handleEditEquipment,\n                onDelete: handleDeleteEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 383,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_DeleteConfirmDialog__WEBPACK_IMPORTED_MODULE_13__.DeleteConfirmDialog, {\n                isOpen: isDeleteDialogOpen,\n                onClose: ()=>setIsDeleteDialogOpen(false),\n                onConfirm: handleConfirmDelete,\n                equipment: selectedEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_equipment_location_modal__WEBPACK_IMPORTED_MODULE_14__.EquipmentLocationModal, {\n                isOpen: isLocationModalOpen,\n                onClose: ()=>setIsLocationModalOpen(false),\n                equipment: rawEquipment,\n                selectedEquipment: selectedEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 398,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(FleetOverviewPage, \"c8RWddfWYkkFPXLK8LvPJ0kfOzg=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast,\n        _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__.useEquipment\n    ];\n});\n_c = FleetOverviewPage;\nvar _c;\n$RefreshReg$(_c, \"FleetOverviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/fleet/page.tsx\n"));

/***/ })

});