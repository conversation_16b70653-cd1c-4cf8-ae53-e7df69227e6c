"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/fleet/page",{

/***/ "(app-pages-browser)/./app/fleet/page.tsx":
/*!****************************!*\
  !*** ./app/fleet/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FleetOverviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,Edit,Eye,MapPin,MoreHorizontal,Plus,RefreshCw,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useEquipment */ \"(app-pages-browser)/./lib/hooks/useEquipment.ts\");\n/* harmony import */ var _lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils/equipment */ \"(app-pages-browser)/./lib/utils/equipment.ts\");\n/* harmony import */ var _components_fleet_EquipmentModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/fleet/EquipmentModal */ \"(app-pages-browser)/./components/fleet/EquipmentModal.tsx\");\n/* harmony import */ var _components_fleet_EquipmentDetailsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/fleet/EquipmentDetailsModal */ \"(app-pages-browser)/./components/fleet/EquipmentDetailsModal.tsx\");\n/* harmony import */ var _components_fleet_DeleteConfirmDialog__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/fleet/DeleteConfirmDialog */ \"(app-pages-browser)/./components/fleet/DeleteConfirmDialog.tsx\");\n/* harmony import */ var _components_ui_equipment_location_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/equipment-location-modal */ \"(app-pages-browser)/./components/ui/equipment-location-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Component now uses real Firestore data via useEquipment hook\nfunction FleetOverviewPage() {\n    _s();\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sourceFilter, setSourceFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Modal states\n    const [isEquipmentModalOpen, setIsEquipmentModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDetailsModalOpen, setIsDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLocationModalOpen, setIsLocationModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedEquipment, setSelectedEquipment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modalMode, setModalMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('create');\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    // Use the custom hook to fetch equipment data\n    const { equipment: rawEquipment, loading, error, refetch, createEquipment, updateEquipment, deleteEquipment } = (0,_lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__.useEquipment)({\n        organizationId: 'demo-org',\n        autoRefresh: true,\n        refreshInterval: 30000 // Refresh every 30 seconds\n    });\n    // Transform Firestore data for UI display\n    const equipmentData = rawEquipment.map(_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.transformEquipmentForUI);\n    // Apply filters\n    const filteredEquipment = (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.filterEquipment)(equipmentData, {\n        status: statusFilter === \"all\" ? undefined : statusFilter,\n        source: sourceFilter === \"all\" ? undefined : sourceFilter,\n        search: searchTerm || undefined\n    });\n    // Get equipment statistics\n    const stats = (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getEquipmentStats)(equipmentData);\n    // CRUD handlers\n    const handleAddEquipment = ()=>{\n        setSelectedEquipment(null);\n        setModalMode('create');\n        setIsEquipmentModalOpen(true);\n    };\n    const handleEditEquipment = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setModalMode('edit');\n        setIsEquipmentModalOpen(true);\n    };\n    const handleViewDetails = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsDetailsModalOpen(true);\n    };\n    const handleDeleteEquipment = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsDeleteDialogOpen(true);\n    };\n    const handleSaveEquipment = async (data)=>{\n        try {\n            if (modalMode === 'create') {\n                await createEquipment(data);\n                toast({\n                    title: \"Equipment Added\",\n                    description: \"\".concat(data.name, \" has been successfully added to your fleet.\")\n                });\n            } else if (selectedEquipment) {\n                await updateEquipment(selectedEquipment.id, data);\n                toast({\n                    title: \"Equipment Updated\",\n                    description: \"\".concat(data.name, \" has been successfully updated.\")\n                });\n            }\n            setIsEquipmentModalOpen(false);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save equipment. Please try again.\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const handleConfirmDelete = async ()=>{\n        if (!selectedEquipment) return;\n        try {\n            await deleteEquipment(selectedEquipment.id);\n            toast({\n                title: \"Equipment Deleted\",\n                description: \"\".concat(selectedEquipment.name, \" has been removed from your fleet.\")\n            });\n            setIsDeleteDialogOpen(false);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete equipment. Please try again.\",\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const handleScheduleMaintenance = (equipment)=>{\n        // TODO: Implement maintenance scheduling\n        toast({\n            title: \"Feature Coming Soon\",\n            description: \"Maintenance scheduling will be available in a future update.\"\n        });\n    };\n    const handleTrackLocation = (equipment)=>{\n        setSelectedEquipment(equipment);\n        setIsLocationModalOpen(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fleet Overview\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Manage and monitor your equipment fleet\",\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600 ml-2\",\n                                        children: \"• Loading...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 25\n                                    }, this),\n                                    !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600 ml-2\",\n                                        children: \"• Live from Firestore\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 26\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: refetch,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleAddEquipment,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Equipment\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-red-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Error loading equipment: \",\n                                    error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: refetch,\n                                className: \"ml-auto\",\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"Search equipment...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pl-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: statusFilter,\n                                onValueChange: setStatusFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-full md:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"active\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"maintenance\",\n                                                children: \"Maintenance\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"inactive\",\n                                                children: \"Inactive\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: sourceFilter,\n                                onValueChange: setSourceFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-full md:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"Source\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Sources\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"samsara\",\n                                                children: \"Samsara\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"manual\",\n                                                children: \"Manual\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Equipment (\",\n                                        filteredEquipment.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Active (\",\n                                                stats.active,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Maintenance (\",\n                                                stats.maintenance,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full bg-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Inactive (\",\n                                                stats.inactive,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400 ml-2\",\n                                            children: [\n                                                \"Utilization: \",\n                                                stats.utilization,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6 animate-spin text-gray-400 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Loading equipment...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this) : filteredEquipment.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: equipmentData.length === 0 ? \"No equipment found. Try adding some equipment or seeding demo data.\" : \"No equipment matches your current filters.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this),\n                                equipmentData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"mt-4\",\n                                    onClick: ()=>window.open('/firestore-demo', '_blank'),\n                                    children: \"Seed Demo Data\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Equipment Name\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Location\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Operator\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Last Update\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                children: \"Source\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                className: \"w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                    children: filteredEquipment.map((equipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                            className: \"hover:bg-gray-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    className: \"font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: equipment.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.make && equipment.model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    equipment.make,\n                                                                    \" \",\n                                                                    equipment.model,\n                                                                    \" \",\n                                                                    equipment.year\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: equipment.type\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat((0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusDot)(equipment.status))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusColor)(equipment.status),\n                                                                children: (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_10__.getStatusDisplayText)(equipment.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.location\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: equipment.operator\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-3 w-3 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            equipment.lastUpdate\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        children: equipment.source\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"icon\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                                                align: \"end\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleViewDetails(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 326,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"View Details\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleEditEquipment(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 330,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Edit Equipment\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleScheduleMaintenance(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 335,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Schedule Maintenance\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleTrackLocation(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Track Location\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                        onClick: ()=>handleDeleteEquipment(rawEquipment.find((e)=>e.id === equipment.id)),\n                                                                        className: \"text-red-600 focus:text-red-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_Edit_Eye_MapPin_MoreHorizontal_Plus_RefreshCw_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \"Delete Equipment\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, equipment.id, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_EquipmentModal__WEBPACK_IMPORTED_MODULE_11__.EquipmentModal, {\n                isOpen: isEquipmentModalOpen,\n                onClose: ()=>setIsEquipmentModalOpen(false),\n                onSave: handleSaveEquipment,\n                equipment: selectedEquipment,\n                mode: modalMode\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_EquipmentDetailsModal__WEBPACK_IMPORTED_MODULE_12__.EquipmentDetailsModal, {\n                isOpen: isDetailsModalOpen,\n                onClose: ()=>setIsDetailsModalOpen(false),\n                equipment: selectedEquipment,\n                onEdit: handleEditEquipment,\n                onDelete: handleDeleteEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fleet_DeleteConfirmDialog__WEBPACK_IMPORTED_MODULE_13__.DeleteConfirmDialog, {\n                isOpen: isDeleteDialogOpen,\n                onClose: ()=>setIsDeleteDialogOpen(false),\n                onConfirm: handleConfirmDelete,\n                equipment: selectedEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_equipment_location_modal__WEBPACK_IMPORTED_MODULE_14__.EquipmentLocationModal, {\n                isOpen: isLocationModalOpen,\n                onClose: ()=>setIsLocationModalOpen(false),\n                equipment: rawEquipment,\n                selectedEquipment: selectedEquipment\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n                lineNumber: 385,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n_s(FleetOverviewPage, \"c8RWddfWYkkFPXLK8LvPJ0kfOzg=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast,\n        _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__.useEquipment\n    ];\n});\n_c = FleetOverviewPage;\nvar _c;\n$RefreshReg$(_c, \"FleetOverviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/fleet/page.tsx\n"));

/***/ })

});