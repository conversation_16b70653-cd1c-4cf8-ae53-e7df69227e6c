"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/fleet/page",{

/***/ "(app-pages-browser)/./components/fleet/EquipmentDetailsModal.tsx":
/*!****************************************************!*\
  !*** ./components/fleet/EquipmentDetailsModal.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EquipmentDetailsModal: () => (/* binding */ EquipmentDetailsModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _lib_utils_equipment__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/equipment */ \"(app-pages-browser)/./lib/utils/equipment.ts\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,FileText,Hash,MapPin,Trash2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,FileText,Hash,MapPin,Trash2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,FileText,Hash,MapPin,Trash2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,FileText,Hash,MapPin,Trash2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,FileText,Hash,MapPin,Trash2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,FileText,Hash,MapPin,Trash2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Edit,FileText,Hash,MapPin,Trash2,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* __next_internal_client_entry_do_not_use__ EquipmentDetailsModal auto */ \n\n\n\n\n\n\nfunction EquipmentDetailsModal(param) {\n    let { isOpen, onClose, equipment, onEdit, onDelete } = param;\n    var _equipment_currentLocation, _equipment_maintenance_currentHours, _equipment_maintenance, _equipment_maintenance1, _equipment_maintenance2, _equipment_maintenance3;\n    if (!equipment) return null;\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'Not set';\n        try {\n            return new Date(dateString).toLocaleDateString();\n        } catch (e) {\n            return 'Invalid date';\n        }\n    };\n    const getMaintenanceStatus = ()=>{\n        var _equipment_maintenance;\n        if (!((_equipment_maintenance = equipment.maintenance) === null || _equipment_maintenance === void 0 ? void 0 : _equipment_maintenance.nextServiceDue)) return 'No schedule';\n        const nextService = new Date(equipment.maintenance.nextServiceDue);\n        const today = new Date();\n        const daysUntilService = Math.ceil((nextService.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        if (daysUntilService < 0) return 'Overdue';\n        if (daysUntilService <= 7) return 'Due soon';\n        return 'On schedule';\n    };\n    const getMaintenanceStatusColor = ()=>{\n        const status = getMaintenanceStatus();\n        switch(status){\n            case 'Overdue':\n                return 'bg-red-100 text-red-800';\n            case 'Due soon':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'On schedule':\n                return 'bg-green-100 text-green-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.DialogContent, {\n            className: \"max-w-3xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.DialogHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.DialogTitle, {\n                                className: \"text-xl\",\n                                children: equipment.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>onEdit(equipment),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Edit\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>onDelete(equipment),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Delete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full \".concat((0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_5__.getStatusDot)(equipment.status))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                            variant: \"secondary\",\n                                            className: (0,_lib_utils_equipment__WEBPACK_IMPORTED_MODULE_5__.getStatusColor)(equipment.status),\n                                            children: equipment.status.charAt(0).toUpperCase() + equipment.status.slice(1)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                    variant: \"outline\",\n                                    className: getMaintenanceStatusColor(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-3 w-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        getMaintenanceStatus()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Equipment Information\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-600 w-20\",\n                                                            children: \"Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: equipment.type\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 17\n                                                }, this),\n                                                equipment.make && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-600 w-20\",\n                                                            children: \"Make:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: equipment.make\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this),\n                                                equipment.model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-600 w-20\",\n                                                            children: \"Model:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: equipment.model\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this),\n                                                equipment.year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-600 w-20\",\n                                                            children: \"Year:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: equipment.year\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                equipment.serialNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-600\",\n                                                            children: \"Serial:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-mono text-sm\",\n                                                            children: equipment.serialNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Location & Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                ((_equipment_currentLocation = equipment.currentLocation) === null || _equipment_currentLocation === void 0 ? void 0 : _equipment_currentLocation.address) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 mt-0.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-600\",\n                                                                    children: \"Location:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                                    lineNumber: 143,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: equipment.currentLocation.address\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-600\",\n                                                            children: \"Updated:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: formatDate(equipment.updatedAt || equipment.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_4__.Separator, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Maintenance Information\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Current Hours\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: ((_equipment_maintenance = equipment.maintenance) === null || _equipment_maintenance === void 0 ? void 0 : (_equipment_maintenance_currentHours = _equipment_maintenance.currentHours) === null || _equipment_maintenance_currentHours === void 0 ? void 0 : _equipment_maintenance_currentHours.toLocaleString()) || '0'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Service Interval\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: [\n                                                        ((_equipment_maintenance1 = equipment.maintenance) === null || _equipment_maintenance1 === void 0 ? void 0 : _equipment_maintenance1.serviceInterval) || 250,\n                                                        \"h\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Last Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: formatDate((_equipment_maintenance2 = equipment.maintenance) === null || _equipment_maintenance2 === void 0 ? void 0 : _equipment_maintenance2.lastServiceDate)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Next Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: formatDate((_equipment_maintenance3 = equipment.maintenance) === null || _equipment_maintenance3 === void 0 ? void 0 : _equipment_maintenance3.nextServiceDue)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        equipment.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_4__.Separator, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Edit_FileText_Hash_MapPin_Trash2_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Description\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 leading-relaxed\",\n                                            children: equipment.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_4__.Separator, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Created:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        formatDate(equipment.createdAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                equipment.updatedAt && equipment.updatedAt !== equipment.createdAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Last Updated:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" \",\n                                        formatDate(equipment.updatedAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/dev/diggit-web/components/fleet/EquipmentDetailsModal.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_c = EquipmentDetailsModal;\nvar _c;\n$RefreshReg$(_c, \"EquipmentDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvZmxlZXQvRXF1aXBtZW50RGV0YWlsc01vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFeUY7QUFDNUM7QUFDRTtBQUNNO0FBRXFDO0FBQ0U7QUFVckYsU0FBU2dCLHNCQUFzQixLQU1UO1FBTlMsRUFDcENDLE1BQU0sRUFDTkMsT0FBTyxFQUNQQyxTQUFTLEVBQ1RDLE1BQU0sRUFDTkMsUUFBUSxFQUNtQixHQU5TO1FBd0hyQkYsNEJBa0NFQSxxQ0FBQUEsd0JBT0FBLHlCQU9XQSx5QkFPQUE7SUF4SzVCLElBQUksQ0FBQ0EsV0FBVyxPQUFPO0lBRXZCLE1BQU1HLGFBQWEsQ0FBQ0M7UUFDbEIsSUFBSSxDQUFDQSxZQUFZLE9BQU87UUFDeEIsSUFBSTtZQUNGLE9BQU8sSUFBSUMsS0FBS0QsWUFBWUUsa0JBQWtCO1FBQ2hELEVBQUUsVUFBTTtZQUNOLE9BQU87UUFDVDtJQUNGO0lBRUEsTUFBTUMsdUJBQXVCO1lBQ3RCUDtRQUFMLElBQUksR0FBQ0EseUJBQUFBLFVBQVVRLFdBQVcsY0FBckJSLDZDQUFBQSx1QkFBdUJTLGNBQWMsR0FBRSxPQUFPO1FBRW5ELE1BQU1DLGNBQWMsSUFBSUwsS0FBS0wsVUFBVVEsV0FBVyxDQUFDQyxjQUFjO1FBQ2pFLE1BQU1FLFFBQVEsSUFBSU47UUFDbEIsTUFBTU8sbUJBQW1CQyxLQUFLQyxJQUFJLENBQUMsQ0FBQ0osWUFBWUssT0FBTyxLQUFLSixNQUFNSSxPQUFPLEVBQUMsSUFBTSxRQUFPLEtBQUssS0FBSyxFQUFDO1FBRWxHLElBQUlILG1CQUFtQixHQUFHLE9BQU87UUFDakMsSUFBSUEsb0JBQW9CLEdBQUcsT0FBTztRQUNsQyxPQUFPO0lBQ1Q7SUFFQSxNQUFNSSw0QkFBNEI7UUFDaEMsTUFBTUMsU0FBU1Y7UUFDZixPQUFRVTtZQUNOLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFZLE9BQU87WUFDeEIsS0FBSztnQkFBZSxPQUFPO1lBQzNCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLHFCQUNFLDhEQUFDcEMseURBQU1BO1FBQUNxQyxNQUFNcEI7UUFBUXFCLGNBQWNwQjtrQkFDbEMsNEVBQUNqQixnRUFBYUE7WUFBQ3NDLFdBQVU7OzhCQUN2Qiw4REFBQ3JDLCtEQUFZQTs4QkFDWCw0RUFBQ3NDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ3BDLDhEQUFXQTtnQ0FBQ29DLFdBQVU7MENBQVdwQixVQUFVc0IsSUFBSTs7Ozs7OzBDQUNoRCw4REFBQ0Q7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDbEMseURBQU1BO3dDQUFDcUMsU0FBUTt3Q0FBVUMsTUFBSzt3Q0FBS0MsU0FBUyxJQUFNeEIsT0FBT0Q7OzBEQUN4RCw4REFBQ0wseUhBQUlBO2dEQUFDeUIsV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7OztrREFHbkMsOERBQUNsQyx5REFBTUE7d0NBQUNxQyxTQUFRO3dDQUFVQyxNQUFLO3dDQUFLQyxTQUFTLElBQU12QixTQUFTRjs7MERBQzFELDhEQUFDSix5SEFBTUE7Z0RBQUN3QixXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTzNDLDhEQUFDQztvQkFBSUQsV0FBVTs7c0NBRWIsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVyx3QkFBdUQsT0FBL0IvQixrRUFBWUEsQ0FBQ1csVUFBVWlCLE1BQU07Ozs7OztzREFDckUsOERBQUNoQyx1REFBS0E7NENBQUNzQyxTQUFROzRDQUFZSCxXQUFXaEMsb0VBQWNBLENBQUNZLFVBQVVpQixNQUFNO3NEQUNsRWpCLFVBQVVpQixNQUFNLENBQUNTLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUszQixVQUFVaUIsTUFBTSxDQUFDVyxLQUFLLENBQUM7Ozs7Ozs7Ozs7Ozs4Q0FHdkUsOERBQUMzQyx1REFBS0E7b0NBQUNzQyxTQUFRO29DQUFVSCxXQUFXSjs7c0RBQ2xDLDhEQUFDeEIseUhBQU1BOzRDQUFDNEIsV0FBVTs7Ozs7O3dDQUNqQmI7Ozs7Ozs7Ozs7Ozs7c0NBS0wsOERBQUNjOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDUzs0Q0FBR1QsV0FBVTtzREFBd0I7Ozs7OztzREFFdEMsOERBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDVTs0REFBS1YsV0FBVTtzRUFBaUM7Ozs7OztzRUFDakQsOERBQUNVO3NFQUFNOUIsVUFBVStCLElBQUk7Ozs7Ozs7Ozs7OztnREFHdEIvQixVQUFVZ0MsSUFBSSxrQkFDYiw4REFBQ1g7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDVTs0REFBS1YsV0FBVTtzRUFBaUM7Ozs7OztzRUFDakQsOERBQUNVO3NFQUFNOUIsVUFBVWdDLElBQUk7Ozs7Ozs7Ozs7OztnREFJeEJoQyxVQUFVaUMsS0FBSyxrQkFDZCw4REFBQ1o7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDVTs0REFBS1YsV0FBVTtzRUFBaUM7Ozs7OztzRUFDakQsOERBQUNVO3NFQUFNOUIsVUFBVWlDLEtBQUs7Ozs7Ozs7Ozs7OztnREFJekJqQyxVQUFVa0MsSUFBSSxrQkFDYiw4REFBQ2I7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDVTs0REFBS1YsV0FBVTtzRUFBaUM7Ozs7OztzRUFDakQsOERBQUNVO3NFQUFNOUIsVUFBVWtDLElBQUk7Ozs7Ozs7Ozs7OztnREFJeEJsQyxVQUFVbUMsWUFBWSxrQkFDckIsOERBQUNkO29EQUFJRCxXQUFVOztzRUFDYiw4REFBQzNCLHlIQUFJQTs0REFBQzJCLFdBQVU7Ozs7OztzRUFDaEIsOERBQUNVOzREQUFLVixXQUFVO3NFQUE0Qjs7Ozs7O3NFQUM1Qyw4REFBQ1U7NERBQUtWLFdBQVU7c0VBQXFCcEIsVUFBVW1DLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNbkUsOERBQUNkO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ1M7NENBQUdULFdBQVU7c0RBQXdCOzs7Ozs7c0RBRXRDLDhEQUFDQzs0Q0FBSUQsV0FBVTs7Z0RBQ1pwQixFQUFBQSw2QkFBQUEsVUFBVW9DLGVBQWUsY0FBekJwQyxpREFBQUEsMkJBQTJCcUMsT0FBTyxtQkFDakMsOERBQUNoQjtvREFBSUQsV0FBVTs7c0VBQ2IsOERBQUM5QiwwSEFBTUE7NERBQUM4QixXQUFVOzs7Ozs7c0VBQ2xCLDhEQUFDQzs7OEVBQ0MsOERBQUNTO29FQUFLVixXQUFVOzhFQUE0Qjs7Ozs7OzhFQUM1Qyw4REFBQ2tCO29FQUFFbEIsV0FBVTs4RUFBV3BCLFVBQVVvQyxlQUFlLENBQUNDLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFLL0QsOERBQUNoQjtvREFBSUQsV0FBVTs7c0VBQ2IsOERBQUM3QiwwSEFBS0E7NERBQUM2QixXQUFVOzs7Ozs7c0VBQ2pCLDhEQUFDVTs0REFBS1YsV0FBVTtzRUFBNEI7Ozs7OztzRUFDNUMsOERBQUNVOzREQUFLVixXQUFVO3NFQUNiakIsV0FBV0gsVUFBVXVDLFNBQVMsSUFBSXZDLFVBQVV3QyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT2hFLDhEQUFDckQsK0RBQVNBOzs7OztzQ0FHViw4REFBQ2tDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ1M7b0NBQUdULFdBQVU7O3NEQUNaLDhEQUFDNUIseUhBQU1BOzRDQUFDNEIsV0FBVTs7Ozs7O3dDQUFZOzs7Ozs7OzhDQUloQyw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNDO29EQUFJRCxXQUFVOzhEQUFvQzs7Ozs7OzhEQUNuRCw4REFBQ0M7b0RBQUlELFdBQVU7OERBQ1pwQixFQUFBQSx5QkFBQUEsVUFBVVEsV0FBVyxjQUFyQlIsOENBQUFBLHNDQUFBQSx1QkFBdUJ5QyxZQUFZLGNBQW5DekMsMERBQUFBLG9DQUFxQzBDLGNBQWMsT0FBTTs7Ozs7Ozs7Ozs7O3NEQUk5RCw4REFBQ3JCOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUlELFdBQVU7OERBQW9DOzs7Ozs7OERBQ25ELDhEQUFDQztvREFBSUQsV0FBVTs7d0RBQ1pwQixFQUFBQSwwQkFBQUEsVUFBVVEsV0FBVyxjQUFyQlIsOENBQUFBLHdCQUF1QjJDLGVBQWUsS0FBSTt3REFBSTs7Ozs7Ozs7Ozs7OztzREFJbkQsOERBQUN0Qjs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNDO29EQUFJRCxXQUFVOzhEQUFvQzs7Ozs7OzhEQUNuRCw4REFBQ0M7b0RBQUlELFdBQVU7OERBQ1pqQixZQUFXSCwwQkFBQUEsVUFBVVEsV0FBVyxjQUFyQlIsOENBQUFBLHdCQUF1QjRDLGVBQWU7Ozs7Ozs7Ozs7OztzREFJdEQsOERBQUN2Qjs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNDO29EQUFJRCxXQUFVOzhEQUFvQzs7Ozs7OzhEQUNuRCw4REFBQ0M7b0RBQUlELFdBQVU7OERBQ1pqQixZQUFXSCwwQkFBQUEsVUFBVVEsV0FBVyxjQUFyQlIsOENBQUFBLHdCQUF1QlMsY0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQU94RFQsVUFBVTZDLFdBQVcsa0JBQ3BCOzs4Q0FDRSw4REFBQzFELCtEQUFTQTs7Ozs7OENBQ1YsOERBQUNrQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNTOzRDQUFHVCxXQUFVOzs4REFDWiw4REFBQzFCLDBIQUFRQTtvREFBQzBCLFdBQVU7Ozs7OztnREFBWTs7Ozs7OztzREFHbEMsOERBQUNrQjs0Q0FBRWxCLFdBQVU7c0RBQWlDcEIsVUFBVTZDLFdBQVc7Ozs7Ozs7Ozs7Ozs7O3NDQU16RSw4REFBQzFELCtEQUFTQTs7Ozs7c0NBQ1YsOERBQUNrQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDOztzREFDQyw4REFBQ1M7NENBQUtWLFdBQVU7c0RBQWM7Ozs7Ozt3Q0FBZTt3Q0FBRWpCLFdBQVdILFVBQVV3QyxTQUFTOzs7Ozs7O2dDQUU5RXhDLFVBQVV1QyxTQUFTLElBQUl2QyxVQUFVdUMsU0FBUyxLQUFLdkMsVUFBVXdDLFNBQVMsa0JBQ2pFLDhEQUFDbkI7O3NEQUNDLDhEQUFDUzs0Q0FBS1YsV0FBVTtzREFBYzs7Ozs7O3dDQUFvQjt3Q0FBRWpCLFdBQVdILFVBQVV1QyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRbEc7S0FuTmdCMUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9jZ3JhbnQvZGV2L2RpZ2dpdC13ZWIvY29tcG9uZW50cy9mbGVldC9FcXVpcG1lbnREZXRhaWxzTW9kYWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IERpYWxvZywgRGlhbG9nQ29udGVudCwgRGlhbG9nSGVhZGVyLCBEaWFsb2dUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9kaWFsb2cnXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9iYWRnZSdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBTZXBhcmF0b3IgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2VwYXJhdG9yJ1xuaW1wb3J0IHsgRXF1aXBtZW50IH0gZnJvbSAnQC9saWIvdHlwZXMvZmlyZXN0b3JlJ1xuaW1wb3J0IHsgZ2V0U3RhdHVzQ29sb3IsIGdldFN0YXR1c0RvdCwgZ2V0U3RhdHVzRGlzcGxheVRleHQgfSBmcm9tICdAL2xpYi91dGlscy9lcXVpcG1lbnQnXG5pbXBvcnQgeyBNYXBQaW4sIENsb2NrLCBXcmVuY2gsIENhbGVuZGFyLCBIYXNoLCBGaWxlVGV4dCwgRWRpdCwgVHJhc2gyIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgRXF1aXBtZW50RGV0YWlsc01vZGFsUHJvcHMge1xuICBpc09wZW46IGJvb2xlYW5cbiAgb25DbG9zZTogKCkgPT4gdm9pZFxuICBlcXVpcG1lbnQ6IEVxdWlwbWVudCB8IG51bGxcbiAgb25FZGl0OiAoZXF1aXBtZW50OiBFcXVpcG1lbnQpID0+IHZvaWRcbiAgb25EZWxldGU6IChlcXVpcG1lbnQ6IEVxdWlwbWVudCkgPT4gdm9pZFxufVxuXG5leHBvcnQgZnVuY3Rpb24gRXF1aXBtZW50RGV0YWlsc01vZGFsKHsgXG4gIGlzT3BlbiwgXG4gIG9uQ2xvc2UsIFxuICBlcXVpcG1lbnQsIFxuICBvbkVkaXQsIFxuICBvbkRlbGV0ZSBcbn06IEVxdWlwbWVudERldGFpbHNNb2RhbFByb3BzKSB7XG4gIGlmICghZXF1aXBtZW50KSByZXR1cm4gbnVsbFxuXG4gIGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZVN0cmluZzogc3RyaW5nIHwgdW5kZWZpbmVkKSA9PiB7XG4gICAgaWYgKCFkYXRlU3RyaW5nKSByZXR1cm4gJ05vdCBzZXQnXG4gICAgdHJ5IHtcbiAgICAgIHJldHVybiBuZXcgRGF0ZShkYXRlU3RyaW5nKS50b0xvY2FsZURhdGVTdHJpbmcoKVxuICAgIH0gY2F0Y2gge1xuICAgICAgcmV0dXJuICdJbnZhbGlkIGRhdGUnXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0TWFpbnRlbmFuY2VTdGF0dXMgPSAoKSA9PiB7XG4gICAgaWYgKCFlcXVpcG1lbnQubWFpbnRlbmFuY2U/Lm5leHRTZXJ2aWNlRHVlKSByZXR1cm4gJ05vIHNjaGVkdWxlJ1xuICAgIFxuICAgIGNvbnN0IG5leHRTZXJ2aWNlID0gbmV3IERhdGUoZXF1aXBtZW50Lm1haW50ZW5hbmNlLm5leHRTZXJ2aWNlRHVlKVxuICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKVxuICAgIGNvbnN0IGRheXNVbnRpbFNlcnZpY2UgPSBNYXRoLmNlaWwoKG5leHRTZXJ2aWNlLmdldFRpbWUoKSAtIHRvZGF5LmdldFRpbWUoKSkgLyAoMTAwMCAqIDYwICogNjAgKiAyNCkpXG4gICAgXG4gICAgaWYgKGRheXNVbnRpbFNlcnZpY2UgPCAwKSByZXR1cm4gJ092ZXJkdWUnXG4gICAgaWYgKGRheXNVbnRpbFNlcnZpY2UgPD0gNykgcmV0dXJuICdEdWUgc29vbidcbiAgICByZXR1cm4gJ09uIHNjaGVkdWxlJ1xuICB9XG5cbiAgY29uc3QgZ2V0TWFpbnRlbmFuY2VTdGF0dXNDb2xvciA9ICgpID0+IHtcbiAgICBjb25zdCBzdGF0dXMgPSBnZXRNYWludGVuYW5jZVN0YXR1cygpXG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ092ZXJkdWUnOiByZXR1cm4gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJ1xuICAgICAgY2FzZSAnRHVlIHNvb24nOiByZXR1cm4gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJ1xuICAgICAgY2FzZSAnT24gc2NoZWR1bGUnOiByZXR1cm4gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCdcbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxEaWFsb2cgb3Blbj17aXNPcGVufSBvbk9wZW5DaGFuZ2U9e29uQ2xvc2V9PlxuICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwibWF4LXctM3hsIG1heC1oLVs5MHZoXSBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgPERpYWxvZ0hlYWRlcj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPERpYWxvZ1RpdGxlIGNsYXNzTmFtZT1cInRleHQteGxcIj57ZXF1aXBtZW50Lm5hbWV9PC9EaWFsb2dUaXRsZT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgb25DbGljaz17KCkgPT4gb25FZGl0KGVxdWlwbWVudCl9PlxuICAgICAgICAgICAgICAgIDxFZGl0IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgRWRpdFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiIG9uQ2xpY2s9eygpID0+IG9uRGVsZXRlKGVxdWlwbWVudCl9PlxuICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICBEZWxldGVcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICB7LyogU3RhdHVzIGFuZCBCYXNpYyBJbmZvICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTMgaC0zIHJvdW5kZWQtZnVsbCAke2dldFN0YXR1c0RvdChlcXVpcG1lbnQuc3RhdHVzKX1gfT48L2Rpdj5cbiAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJzZWNvbmRhcnlcIiBjbGFzc05hbWU9e2dldFN0YXR1c0NvbG9yKGVxdWlwbWVudC5zdGF0dXMpfT5cbiAgICAgICAgICAgICAgICB7ZXF1aXBtZW50LnN0YXR1cy5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIGVxdWlwbWVudC5zdGF0dXMuc2xpY2UoMSl9XG4gICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT17Z2V0TWFpbnRlbmFuY2VTdGF0dXNDb2xvcigpfT5cbiAgICAgICAgICAgICAgPFdyZW5jaCBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICB7Z2V0TWFpbnRlbmFuY2VTdGF0dXMoKX1cbiAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRXF1aXBtZW50IERldGFpbHMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+RXF1aXBtZW50IEluZm9ybWF0aW9uPC9oMz5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMCB3LTIwXCI+VHlwZTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj57ZXF1aXBtZW50LnR5cGV9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIHtlcXVpcG1lbnQubWFrZSAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDAgdy0yMFwiPk1ha2U6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57ZXF1aXBtZW50Lm1ha2V9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICB7ZXF1aXBtZW50Lm1vZGVsICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMCB3LTIwXCI+TW9kZWw6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57ZXF1aXBtZW50Lm1vZGVsfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAge2VxdWlwbWVudC55ZWFyICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMCB3LTIwXCI+WWVhcjo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPntlcXVpcG1lbnQueWVhcn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIHtlcXVpcG1lbnQuc2VyaWFsTnVtYmVyICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPEhhc2ggY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPlNlcmlhbDo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LXNtXCI+e2VxdWlwbWVudC5zZXJpYWxOdW1iZXJ9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZFwiPkxvY2F0aW9uICYgU3RhdHVzPC9oMz5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAge2VxdWlwbWVudC5jdXJyZW50TG9jYXRpb24/LmFkZHJlc3MgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxNYXBQaW4gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNDAwIG10LTAuNVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPkxvY2F0aW9uOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e2VxdWlwbWVudC5jdXJyZW50TG9jYXRpb24uYWRkcmVzc308L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDBcIj5VcGRhdGVkOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdERhdGUoZXF1aXBtZW50LnVwZGF0ZWRBdCB8fCBlcXVpcG1lbnQuY3JlYXRlZEF0KX1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxTZXBhcmF0b3IgLz5cblxuICAgICAgICAgIHsvKiBNYWludGVuYW5jZSBJbmZvcm1hdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICA8V3JlbmNoIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICBNYWludGVuYW5jZSBJbmZvcm1hdGlvblxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBwLTQgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+Q3VycmVudCBIb3VyczwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+XG4gICAgICAgICAgICAgICAgICB7ZXF1aXBtZW50Lm1haW50ZW5hbmNlPy5jdXJyZW50SG91cnM/LnRvTG9jYWxlU3RyaW5nKCkgfHwgJzAnfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBwLTQgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+U2VydmljZSBJbnRlcnZhbDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+XG4gICAgICAgICAgICAgICAgICB7ZXF1aXBtZW50Lm1haW50ZW5hbmNlPy5zZXJ2aWNlSW50ZXJ2YWwgfHwgMjUwfWhcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcC00IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMFwiPkxhc3QgU2VydmljZTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+XG4gICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZShlcXVpcG1lbnQubWFpbnRlbmFuY2U/Lmxhc3RTZXJ2aWNlRGF0ZSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHAtNCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDBcIj5OZXh0IFNlcnZpY2U8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZFwiPlxuICAgICAgICAgICAgICAgICAge2Zvcm1hdERhdGUoZXF1aXBtZW50Lm1haW50ZW5hbmNlPy5uZXh0U2VydmljZUR1ZSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRGVzY3JpcHRpb24gKi99XG4gICAgICAgICAge2VxdWlwbWVudC5kZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICA8U2VwYXJhdG9yIC8+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgICAgRGVzY3JpcHRpb25cbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgbGVhZGluZy1yZWxheGVkXCI+e2VxdWlwbWVudC5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHsvKiBUaW1lc3RhbXBzICovfVxuICAgICAgICAgIDxTZXBhcmF0b3IgLz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTQgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPkNyZWF0ZWQ6PC9zcGFuPiB7Zm9ybWF0RGF0ZShlcXVpcG1lbnQuY3JlYXRlZEF0KX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAge2VxdWlwbWVudC51cGRhdGVkQXQgJiYgZXF1aXBtZW50LnVwZGF0ZWRBdCAhPT0gZXF1aXBtZW50LmNyZWF0ZWRBdCAmJiAoXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5MYXN0IFVwZGF0ZWQ6PC9zcGFuPiB7Zm9ybWF0RGF0ZShlcXVpcG1lbnQudXBkYXRlZEF0KX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICA8L0RpYWxvZz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkRpYWxvZyIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dIZWFkZXIiLCJEaWFsb2dUaXRsZSIsIkJhZGdlIiwiQnV0dG9uIiwiU2VwYXJhdG9yIiwiZ2V0U3RhdHVzQ29sb3IiLCJnZXRTdGF0dXNEb3QiLCJNYXBQaW4iLCJDbG9jayIsIldyZW5jaCIsIkhhc2giLCJGaWxlVGV4dCIsIkVkaXQiLCJUcmFzaDIiLCJFcXVpcG1lbnREZXRhaWxzTW9kYWwiLCJpc09wZW4iLCJvbkNsb3NlIiwiZXF1aXBtZW50Iiwib25FZGl0Iiwib25EZWxldGUiLCJmb3JtYXREYXRlIiwiZGF0ZVN0cmluZyIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJnZXRNYWludGVuYW5jZVN0YXR1cyIsIm1haW50ZW5hbmNlIiwibmV4dFNlcnZpY2VEdWUiLCJuZXh0U2VydmljZSIsInRvZGF5IiwiZGF5c1VudGlsU2VydmljZSIsIk1hdGgiLCJjZWlsIiwiZ2V0VGltZSIsImdldE1haW50ZW5hbmNlU3RhdHVzQ29sb3IiLCJzdGF0dXMiLCJvcGVuIiwib25PcGVuQ2hhbmdlIiwiY2xhc3NOYW1lIiwiZGl2IiwibmFtZSIsInZhcmlhbnQiLCJzaXplIiwib25DbGljayIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJoMyIsInNwYW4iLCJ0eXBlIiwibWFrZSIsIm1vZGVsIiwieWVhciIsInNlcmlhbE51bWJlciIsImN1cnJlbnRMb2NhdGlvbiIsImFkZHJlc3MiLCJwIiwidXBkYXRlZEF0IiwiY3JlYXRlZEF0IiwiY3VycmVudEhvdXJzIiwidG9Mb2NhbGVTdHJpbmciLCJzZXJ2aWNlSW50ZXJ2YWwiLCJsYXN0U2VydmljZURhdGUiLCJkZXNjcmlwdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/fleet/EquipmentDetailsModal.tsx\n"));

/***/ })

});