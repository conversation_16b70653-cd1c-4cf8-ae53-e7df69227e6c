"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/fleet/page",{

/***/ "(app-pages-browser)/./lib/utils/equipment.ts":
/*!********************************!*\
  !*** ./lib/utils/equipment.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   equipmentStatusOptions: () => (/* binding */ equipmentStatusOptions),\n/* harmony export */   equipmentTypeOptions: () => (/* binding */ equipmentTypeOptions),\n/* harmony export */   filterEquipment: () => (/* binding */ filterEquipment),\n/* harmony export */   getEquipmentStats: () => (/* binding */ getEquipmentStats),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getStatusDisplayText: () => (/* binding */ getStatusDisplayText),\n/* harmony export */   getStatusDot: () => (/* binding */ getStatusDot),\n/* harmony export */   transformEquipmentForUI: () => (/* binding */ transformEquipmentForUI)\n/* harmony export */ });\nfunction transformEquipmentForUI(equipment) {\n    var _equipment_maintenance, _equipment_maintenance1;\n    // Map Firestore status to UI status - preserve distinction between available and in_use\n    const statusMap = {\n        'available': 'available',\n        'in_use': 'in_use',\n        'maintenance': 'maintenance',\n        'repair': 'repair',\n        'out_of_service': 'inactive'\n    };\n    // Map Firestore type to display type\n    const typeMap = {\n        'excavator': 'Excavator',\n        'dump_truck': 'Dump Truck',\n        'backhoe': 'Backhoe',\n        'bulldozer': 'Bulldozer',\n        'crane': 'Crane',\n        'compactor': 'Compactor',\n        'loader': 'Loader',\n        'grader': 'Motor Grader',\n        'skid_steer': 'Skid Steer',\n        'utility_truck': 'Utility Truck',\n        'other': 'Other'\n    };\n    // Calculate time since last update\n    const getTimeAgo = (timestamp)=>{\n        const now = new Date();\n        const then = new Date(timestamp);\n        const diffMs = now.getTime() - then.getTime();\n        const diffMins = Math.floor(diffMs / (1000 * 60));\n        const diffHours = Math.floor(diffMins / 60);\n        const diffDays = Math.floor(diffHours / 24);\n        if (diffMins < 1) return 'Just now';\n        if (diffMins < 60) return \"\".concat(diffMins, \" min ago\");\n        if (diffHours < 24) return \"\".concat(diffHours, \" hour\").concat(diffHours > 1 ? 's' : '', \" ago\");\n        return \"\".concat(diffDays, \" day\").concat(diffDays > 1 ? 's' : '', \" ago\");\n    };\n    // Determine data source (for now, assume Samsara for active equipment, Manual for others)\n    const getDataSource = (status)=>{\n        return [\n            'available',\n            'in_use'\n        ].includes(status) ? 'Samsara' : 'Manual';\n    };\n    // Get operator info (placeholder for now)\n    const getOperator = (status)=>{\n        if (status === 'in_use') {\n            // In a real app, this would come from job assignments\n            const operators = [\n                'Mike Johnson',\n                'Carlos Rodriguez',\n                'Sarah Chen',\n                'David Kim'\n            ];\n            return operators[Math.floor(Math.random() * operators.length)];\n        }\n        return 'Unassigned';\n    };\n    // Get location display\n    const getLocationDisplay = (equipment)=>{\n        var _equipment_currentLocation;\n        if ((_equipment_currentLocation = equipment.currentLocation) === null || _equipment_currentLocation === void 0 ? void 0 : _equipment_currentLocation.address) {\n            return equipment.currentLocation.address;\n        }\n        // Fallback based on status\n        switch(equipment.status){\n            case 'maintenance':\n            case 'repair':\n                return 'Shop';\n            case 'out_of_service':\n                return 'Depot';\n            case 'available':\n                return 'Depot';\n            case 'in_use':\n                return \"Job Site \".concat(String.fromCharCode(65 + Math.floor(Math.random() * 3))) // A, B, or C\n                ;\n            default:\n                return 'Unknown';\n        }\n    };\n    return {\n        id: equipment.id,\n        name: equipment.name,\n        type: typeMap[equipment.type] || equipment.type,\n        status: statusMap[equipment.status] || equipment.status,\n        location: getLocationDisplay(equipment),\n        lastUpdate: getTimeAgo(equipment.updatedAt || equipment.createdAt),\n        source: getDataSource(equipment.status),\n        operator: getOperator(equipment.status),\n        make: equipment.make,\n        model: equipment.model,\n        year: equipment.year,\n        currentHours: (_equipment_maintenance = equipment.maintenance) === null || _equipment_maintenance === void 0 ? void 0 : _equipment_maintenance.currentHours,\n        nextServiceDue: ((_equipment_maintenance1 = equipment.maintenance) === null || _equipment_maintenance1 === void 0 ? void 0 : _equipment_maintenance1.nextServiceDue) ? typeof equipment.maintenance.nextServiceDue === 'string' ? new Date(equipment.maintenance.nextServiceDue).toLocaleDateString() : equipment.maintenance.nextServiceDue.seconds ? new Date(equipment.maintenance.nextServiceDue.seconds * 1000).toLocaleDateString() : new Date(equipment.maintenance.nextServiceDue).toLocaleDateString() : undefined\n    };\n}\n// Status color utilities (matching the existing UI)\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"available\":\n            return \"bg-green-100 text-green-800\";\n        case \"in_use\":\n            return \"bg-blue-100 text-blue-800\";\n        case \"maintenance\":\n            return \"bg-yellow-100 text-yellow-800\";\n        case \"repair\":\n            return \"bg-orange-100 text-orange-800\";\n        case \"inactive\":\n        case \"out_of_service\":\n            return \"bg-red-100 text-red-800\";\n        // Legacy support for old \"active\" status\n        case \"active\":\n            return \"bg-green-100 text-green-800\";\n        default:\n            return \"bg-gray-100 text-gray-800\";\n    }\n};\nconst getStatusDot = (status)=>{\n    switch(status){\n        case \"available\":\n            return \"bg-green-400\";\n        case \"in_use\":\n            return \"bg-blue-400\";\n        case \"maintenance\":\n            return \"bg-yellow-400\";\n        case \"repair\":\n            return \"bg-orange-400\";\n        case \"inactive\":\n        case \"out_of_service\":\n            return \"bg-red-400\";\n        // Legacy support for old \"active\" status\n        case \"active\":\n            return \"bg-green-400\";\n        default:\n            return \"bg-gray-400\";\n    }\n};\n// Get user-friendly status display text\nconst getStatusDisplayText = (status)=>{\n    switch(status){\n        case \"available\":\n            return \"Available\";\n        case \"in_use\":\n            return \"In Use\";\n        case \"maintenance\":\n            return \"Maintenance\";\n        case \"repair\":\n            return \"Repair\";\n        case \"inactive\":\n            return \"Inactive\";\n        case \"out_of_service\":\n            return \"Out of Service\";\n        // Legacy support\n        case \"active\":\n            return \"Active\";\n        default:\n            return status.charAt(0).toUpperCase() + status.slice(1);\n    }\n};\n// Filter utilities\nfunction filterEquipment(equipment, filters) {\n    return equipment.filter((item)=>{\n        const matchesStatus = !filters.status || filters.status === \"all\" || item.status === filters.status;\n        const matchesSource = !filters.source || filters.source === \"all\" || item.source.toLowerCase() === filters.source;\n        const matchesSearch = !filters.search || item.name.toLowerCase().includes(filters.search.toLowerCase()) || item.type.toLowerCase().includes(filters.search.toLowerCase());\n        return matchesStatus && matchesSource && matchesSearch;\n    });\n}\n// Equipment statistics\nfunction getEquipmentStats(equipment) {\n    const total = equipment.length;\n    const available = equipment.filter((e)=>e.status === 'available').length;\n    const inUse = equipment.filter((e)=>e.status === 'in_use').length;\n    const maintenance = equipment.filter((e)=>e.status === 'maintenance' || e.status === 'repair').length;\n    const inactive = equipment.filter((e)=>e.status === 'inactive' || e.status === 'out_of_service').length;\n    // For backward compatibility, calculate \"active\" as available + in_use\n    const active = available + inUse;\n    return {\n        total,\n        available,\n        inUse,\n        active,\n        maintenance,\n        inactive,\n        utilization: total > 0 ? Math.round(inUse / total * 100) : 0 // Utilization based on in_use only\n    };\n}\n// Equipment type options for forms\nconst equipmentTypeOptions = [\n    {\n        value: 'excavator',\n        label: 'Excavator'\n    },\n    {\n        value: 'dump_truck',\n        label: 'Dump Truck'\n    },\n    {\n        value: 'backhoe',\n        label: 'Backhoe'\n    },\n    {\n        value: 'bulldozer',\n        label: 'Bulldozer'\n    },\n    {\n        value: 'crane',\n        label: 'Crane'\n    },\n    {\n        value: 'compactor',\n        label: 'Compactor'\n    },\n    {\n        value: 'loader',\n        label: 'Loader'\n    },\n    {\n        value: 'grader',\n        label: 'Motor Grader'\n    },\n    {\n        value: 'skid_steer',\n        label: 'Skid Steer'\n    },\n    {\n        value: 'utility_truck',\n        label: 'Utility Truck'\n    },\n    {\n        value: 'other',\n        label: 'Other'\n    }\n];\n// Equipment status options for forms\nconst equipmentStatusOptions = [\n    {\n        value: 'available',\n        label: 'Available'\n    },\n    {\n        value: 'in_use',\n        label: 'In Use'\n    },\n    {\n        value: 'maintenance',\n        label: 'Maintenance'\n    },\n    {\n        value: 'repair',\n        label: 'Repair'\n    },\n    {\n        value: 'out_of_service',\n        label: 'Out of Service'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/utils/equipment.ts\n"));

/***/ })

});