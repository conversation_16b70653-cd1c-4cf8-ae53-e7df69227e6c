"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/team-members/page",{

/***/ "(app-pages-browser)/./lib/hooks/useTeamMembers.ts":
/*!*************************************!*\
  !*** ./lib/hooks/useTeamMembers.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTeamMembers: () => (/* binding */ useTeamMembers)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase/config */ \"(app-pages-browser)/./lib/firebase/config.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ useTeamMembers auto */ \n\n\nfunction useTeamMembers() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { organizationId = 'demo-org', autoRefresh = false, refreshInterval = 30000, status, department } = options;\n    const [teamMembers, setTeamMembers] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchTeamMembers = async ()=>{\n        try {\n            console.log('👥 Fetching team members for organization:', organizationId);\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                console.error('❌ Firestore not initialized');\n                throw new Error('Firestore not initialized');\n            }\n            console.log('✅ Firestore is available, building query...');\n            // Build query\n            let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)('profile.firstName', 'asc'));\n            // Add filters if provided\n            if (status) {\n                q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('availability.status', '==', status));\n            }\n            if (department) {\n                q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('department', '==', department));\n            }\n            console.log('🔍 Executing Firestore query...');\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            const members = [];\n            querySnapshot.forEach((doc)=>{\n                members.push({\n                    id: doc.id,\n                    ...doc.data()\n                });\n            });\n            console.log(\"✅ Found \".concat(members.length, \" team members\"));\n            setTeamMembers(members);\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('❌ Error fetching team members:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createTeamMember = async (data)=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            // Prepare the data with timestamps\n            const teamMemberData = {\n                ...data,\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now(),\n                // Ensure required fields have defaults\n                availability: {\n                    status: 'available',\n                    ...data.availability\n                },\n                skills: data.skills || {},\n                profile: {\n                    firstName: '',\n                    lastName: '',\n                    ...data.profile\n                }\n            };\n            const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers'), teamMemberData);\n            const newMember = {\n                id: docRef.id,\n                ...teamMemberData\n            };\n            // Add to local state\n            setTeamMembers((prev)=>[\n                    ...prev,\n                    newMember\n                ]);\n            return newMember;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error creating team member:', err);\n            return null;\n        }\n    };\n    const updateTeamMember = async (id, data)=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            // Prepare update data with timestamp\n            const updateData = {\n                ...data,\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now()\n            };\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers', id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(docRef, updateData);\n            // Update local state\n            setTeamMembers((prev)=>prev.map((member)=>member.id === id ? {\n                        ...member,\n                        ...updateData\n                    } : member));\n            const updatedMember = teamMembers.find((m)=>m.id === id);\n            return updatedMember ? {\n                ...updatedMember,\n                ...updateData\n            } : null;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error updating team member:', err);\n            return null;\n        }\n    };\n    const updateAvailability = async (id, availability)=>{\n        return updateTeamMember(id, {\n            availability\n        });\n    };\n    const deleteTeamMember = async (id)=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers', id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)(docRef);\n            // Remove from local state\n            setTeamMembers((prev)=>prev.filter((member)=>member.id !== id));\n            return true;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error deleting team member:', err);\n            return false;\n        }\n    };\n    // Initial fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTeamMembers.useEffect\": ()=>{\n            fetchTeamMembers();\n        }\n    }[\"useTeamMembers.useEffect\"], [\n        organizationId,\n        status,\n        department\n    ]);\n    // Auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTeamMembers.useEffect\": ()=>{\n            if (!autoRefresh) return;\n            const interval = setInterval(fetchTeamMembers, refreshInterval);\n            return ({\n                \"useTeamMembers.useEffect\": ()=>clearInterval(interval)\n            })[\"useTeamMembers.useEffect\"];\n        }\n    }[\"useTeamMembers.useEffect\"], [\n        autoRefresh,\n        refreshInterval,\n        organizationId,\n        status,\n        department\n    ]);\n    return {\n        teamMembers,\n        loading,\n        error,\n        refetch: fetchTeamMembers,\n        createTeamMember,\n        updateTeamMember,\n        deleteTeamMember,\n        updateAvailability\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/hooks/useTeamMembers.ts\n"));

/***/ })

});