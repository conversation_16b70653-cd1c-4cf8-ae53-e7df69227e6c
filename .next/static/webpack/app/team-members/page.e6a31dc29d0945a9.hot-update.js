"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/team-members/page",{

/***/ "(app-pages-browser)/./lib/hooks/useTeamMembers.ts":
/*!*************************************!*\
  !*** ./lib/hooks/useTeamMembers.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTeamMembers: () => (/* binding */ useTeamMembers)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase/config */ \"(app-pages-browser)/./lib/firebase/config.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ useTeamMembers auto */ \n\n\n// Helper function to remove undefined values from objects\nconst cleanData = (obj)=>{\n    if (obj === null || obj === undefined) return obj;\n    if (typeof obj !== 'object') return obj;\n    if (Array.isArray(obj)) return obj.map(cleanData);\n    const cleaned = {};\n    for (const [key, value] of Object.entries(obj)){\n        if (value !== undefined) {\n            cleaned[key] = cleanData(value);\n        }\n    }\n    return cleaned;\n};\nfunction useTeamMembers() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { organizationId = 'demo-org', autoRefresh = false, refreshInterval = 30000, status, department } = options;\n    const [teamMembers, setTeamMembers] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchTeamMembers = async ()=>{\n        try {\n            console.log('👥 Fetching team members for organization:', organizationId);\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                console.error('❌ Firestore not initialized');\n                throw new Error('Firestore not initialized');\n            }\n            console.log('✅ Firestore is available, building query...');\n            // Build query\n            let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)('profile.firstName', 'asc'));\n            // Add filters if provided\n            if (status) {\n                q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('availability.status', '==', status));\n            }\n            if (department) {\n                q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('department', '==', department));\n            }\n            console.log('🔍 Executing Firestore query...');\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            const members = [];\n            querySnapshot.forEach((doc)=>{\n                members.push({\n                    id: doc.id,\n                    ...doc.data()\n                });\n            });\n            console.log(\"✅ Found \".concat(members.length, \" team members\"));\n            setTeamMembers(members);\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('❌ Error fetching team members:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createTeamMember = async (data)=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            // Prepare the data with timestamps\n            const teamMemberData = {\n                ...data,\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now(),\n                // Ensure required fields have defaults\n                availability: {\n                    status: 'available',\n                    ...data.availability\n                },\n                skills: data.skills || {},\n                profile: {\n                    firstName: '',\n                    lastName: '',\n                    ...data.profile\n                }\n            };\n            const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers'), teamMemberData);\n            const newMember = {\n                id: docRef.id,\n                ...teamMemberData\n            };\n            // Add to local state\n            setTeamMembers((prev)=>[\n                    ...prev,\n                    newMember\n                ]);\n            return newMember;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error creating team member:', err);\n            return null;\n        }\n    };\n    const updateTeamMember = async (id, data)=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            // Prepare update data with timestamp\n            const updateData = {\n                ...data,\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now()\n            };\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers', id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(docRef, updateData);\n            // Update local state\n            setTeamMembers((prev)=>prev.map((member)=>member.id === id ? {\n                        ...member,\n                        ...updateData\n                    } : member));\n            const updatedMember = teamMembers.find((m)=>m.id === id);\n            return updatedMember ? {\n                ...updatedMember,\n                ...updateData\n            } : null;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error updating team member:', err);\n            return null;\n        }\n    };\n    const updateAvailability = async (id, availability)=>{\n        return updateTeamMember(id, {\n            availability\n        });\n    };\n    const deleteTeamMember = async (id)=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers', id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)(docRef);\n            // Remove from local state\n            setTeamMembers((prev)=>prev.filter((member)=>member.id !== id));\n            return true;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error deleting team member:', err);\n            return false;\n        }\n    };\n    // Initial fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTeamMembers.useEffect\": ()=>{\n            fetchTeamMembers();\n        }\n    }[\"useTeamMembers.useEffect\"], [\n        organizationId,\n        status,\n        department\n    ]);\n    // Auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTeamMembers.useEffect\": ()=>{\n            if (!autoRefresh) return;\n            const interval = setInterval(fetchTeamMembers, refreshInterval);\n            return ({\n                \"useTeamMembers.useEffect\": ()=>clearInterval(interval)\n            })[\"useTeamMembers.useEffect\"];\n        }\n    }[\"useTeamMembers.useEffect\"], [\n        autoRefresh,\n        refreshInterval,\n        organizationId,\n        status,\n        department\n    ]);\n    return {\n        teamMembers,\n        loading,\n        error,\n        refetch: fetchTeamMembers,\n        createTeamMember,\n        updateTeamMember,\n        deleteTeamMember,\n        updateAvailability\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/hooks/useTeamMembers.ts\n"));

/***/ })

});