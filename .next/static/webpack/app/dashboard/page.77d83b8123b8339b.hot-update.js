"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./lib/hooks/useTeamMembers.ts":
/*!*************************************!*\
  !*** ./lib/hooks/useTeamMembers.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTeamMembers: () => (/* binding */ useTeamMembers)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase/config */ \"(app-pages-browser)/./lib/firebase/config.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ useTeamMembers auto */ \n\n\nfunction useTeamMembers() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { organizationId = 'demo-org', autoRefresh = false, refreshInterval = 30000, status, department } = options;\n    const [teamMembers, setTeamMembers] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchTeamMembers = async ()=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            // Build query\n            let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)('profile.firstName', 'asc'));\n            // Add filters if provided\n            if (status) {\n                q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('availability.status', '==', status));\n            }\n            if (department) {\n                q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('department', '==', department));\n            }\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            const members = [];\n            querySnapshot.forEach((doc)=>{\n                members.push({\n                    id: doc.id,\n                    ...doc.data()\n                });\n            });\n            setTeamMembers(members);\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error fetching team members:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createTeamMember = async (data)=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            // Prepare the data with timestamps\n            const teamMemberData = {\n                ...data,\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now(),\n                // Ensure required fields have defaults\n                availability: {\n                    status: 'available',\n                    ...data.availability\n                },\n                skills: data.skills || {},\n                profile: {\n                    firstName: '',\n                    lastName: '',\n                    ...data.profile\n                }\n            };\n            const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers'), teamMemberData);\n            const newMember = {\n                id: docRef.id,\n                ...teamMemberData\n            };\n            // Add to local state\n            setTeamMembers((prev)=>[\n                    ...prev,\n                    newMember\n                ]);\n            return newMember;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error creating team member:', err);\n            return null;\n        }\n    };\n    const updateTeamMember = async (id, data)=>{\n        try {\n            setError(null);\n            const response = await fetch(\"/api/team-members/\".concat(id), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...data,\n                    orgId: organizationId\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to update team member');\n            }\n            if (result.success) {\n                // Update the local state\n                setTeamMembers((prev)=>prev.map((member)=>member.id === id ? result.data : member));\n                return result.data;\n            } else {\n                throw new Error(result.error || 'Failed to update team member');\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error updating team member:', err);\n            return null;\n        }\n    };\n    const updateAvailability = async (id, availability)=>{\n        return updateTeamMember(id, {\n            availability\n        });\n    };\n    const deleteTeamMember = async (id)=>{\n        try {\n            setError(null);\n            const response = await fetch(\"/api/team-members/\".concat(id, \"?orgId=\").concat(organizationId), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to delete team member');\n            }\n            if (result.success) {\n                // Remove from local state\n                setTeamMembers((prev)=>prev.filter((member)=>member.id !== id));\n                return true;\n            } else {\n                throw new Error(result.error || 'Failed to delete team member');\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error deleting team member:', err);\n            return false;\n        }\n    };\n    // Initial fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTeamMembers.useEffect\": ()=>{\n            fetchTeamMembers();\n        }\n    }[\"useTeamMembers.useEffect\"], [\n        organizationId,\n        status,\n        department\n    ]);\n    // Auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTeamMembers.useEffect\": ()=>{\n            if (!autoRefresh) return;\n            const interval = setInterval(fetchTeamMembers, refreshInterval);\n            return ({\n                \"useTeamMembers.useEffect\": ()=>clearInterval(interval)\n            })[\"useTeamMembers.useEffect\"];\n        }\n    }[\"useTeamMembers.useEffect\"], [\n        autoRefresh,\n        refreshInterval,\n        organizationId,\n        status,\n        department\n    ]);\n    return {\n        teamMembers,\n        loading,\n        error,\n        refetch: fetchTeamMembers,\n        createTeamMember,\n        updateTeamMember,\n        deleteTeamMember,\n        updateAvailability\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9ob29rcy91c2VUZWFtTWVtYmVycy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztvRUFFMkM7QUFFRDtBQVlmO0FBcUJwQixTQUFTVTtRQUFlQyxVQUFBQSxpRUFBaUMsQ0FBQztJQUMvRCxNQUFNLEVBQ0pDLGlCQUFpQixVQUFVLEVBQzNCQyxjQUFjLEtBQUssRUFDbkJDLGtCQUFrQixLQUFLLEVBQ3ZCQyxNQUFNLEVBQ05DLFVBQVUsRUFDWCxHQUFHTDtJQUVKLE1BQU0sQ0FBQ00sYUFBYUMsZUFBZSxHQUFHbEIsK0NBQVFBLENBQWUsRUFBRTtJQUMvRCxNQUFNLENBQUNtQixTQUFTQyxXQUFXLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNxQixPQUFPQyxTQUFTLEdBQUd0QiwrQ0FBUUEsQ0FBZ0I7SUFFbEQsTUFBTXVCLG1CQUFtQjtRQUN2QixJQUFJO1lBQ0ZELFNBQVM7WUFFVCxJQUFJLENBQUNwQixvREFBRUEsRUFBRTtnQkFDUCxNQUFNLElBQUlzQixNQUFNO1lBQ2xCO1lBRUEsY0FBYztZQUNkLElBQUlDLElBQUlyQix5REFBS0EsQ0FDWEQsOERBQVVBLENBQUNELG9EQUFFQSxFQUFFLGlCQUFpQlUsZ0JBQWdCLGdCQUNoREosMkRBQU9BLENBQUMscUJBQXFCO1lBRy9CLDBCQUEwQjtZQUMxQixJQUFJTyxRQUFRO2dCQUNWVSxJQUFJckIseURBQUtBLENBQUNxQixHQUFHcEIseURBQUtBLENBQUMsdUJBQXVCLE1BQU1VO1lBQ2xEO1lBQ0EsSUFBSUMsWUFBWTtnQkFDZFMsSUFBSXJCLHlEQUFLQSxDQUFDcUIsR0FBR3BCLHlEQUFLQSxDQUFDLGNBQWMsTUFBTVc7WUFDekM7WUFFQSxNQUFNVSxnQkFBZ0IsTUFBTXBCLDJEQUFPQSxDQUFDbUI7WUFDcEMsTUFBTUUsVUFBd0IsRUFBRTtZQUVoQ0QsY0FBY0UsT0FBTyxDQUFDLENBQUNDO2dCQUNyQkYsUUFBUUcsSUFBSSxDQUFDO29CQUNYQyxJQUFJRixJQUFJRSxFQUFFO29CQUNWLEdBQUdGLElBQUlHLElBQUksRUFBRTtnQkFDZjtZQUNGO1lBRUFkLGVBQWVTO1FBQ2pCLEVBQUUsT0FBT00sS0FBSztZQUNaLE1BQU1DLGVBQWVELGVBQWVULFFBQVFTLElBQUlFLE9BQU8sR0FBRztZQUMxRGIsU0FBU1k7WUFDVEUsUUFBUWYsS0FBSyxDQUFDLGdDQUFnQ1k7UUFDaEQsU0FBVTtZQUNSYixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1pQixtQkFBbUIsT0FBT0w7UUFDOUIsSUFBSTtZQUNGVixTQUFTO1lBRVQsSUFBSSxDQUFDcEIsb0RBQUVBLEVBQUU7Z0JBQ1AsTUFBTSxJQUFJc0IsTUFBTTtZQUNsQjtZQUVBLG1DQUFtQztZQUNuQyxNQUFNYyxpQkFBaUI7Z0JBQ3JCLEdBQUdOLElBQUk7Z0JBQ1BPLFdBQVc5Qix5REFBU0EsQ0FBQytCLEdBQUc7Z0JBQ3hCQyxXQUFXaEMseURBQVNBLENBQUMrQixHQUFHO2dCQUN4Qix1Q0FBdUM7Z0JBQ3ZDRSxjQUFjO29CQUNaM0IsUUFBUTtvQkFDUixHQUFHaUIsS0FBS1UsWUFBWTtnQkFDdEI7Z0JBQ0FDLFFBQVFYLEtBQUtXLE1BQU0sSUFBSSxDQUFDO2dCQUN4QkMsU0FBUztvQkFDUEMsV0FBVztvQkFDWEMsVUFBVTtvQkFDVixHQUFHZCxLQUFLWSxPQUFPO2dCQUNqQjtZQUNGO1lBRUEsTUFBTUcsU0FBUyxNQUFNeEMsMERBQU1BLENBQ3pCSiw4REFBVUEsQ0FBQ0Qsb0RBQUVBLEVBQUUsaUJBQWlCVSxnQkFBZ0IsZ0JBQ2hEMEI7WUFHRixNQUFNVSxZQUFZO2dCQUNoQmpCLElBQUlnQixPQUFPaEIsRUFBRTtnQkFDYixHQUFHTyxjQUFjO1lBQ25CO1lBRUEscUJBQXFCO1lBQ3JCcEIsZUFBZStCLENBQUFBLE9BQVE7dUJBQUlBO29CQUFNRDtpQkFBVTtZQUUzQyxPQUFPQTtRQUNULEVBQUUsT0FBT2YsS0FBSztZQUNaLE1BQU1DLGVBQWVELGVBQWVULFFBQVFTLElBQUlFLE9BQU8sR0FBRztZQUMxRGIsU0FBU1k7WUFDVEUsUUFBUWYsS0FBSyxDQUFDLCtCQUErQlk7WUFDN0MsT0FBTztRQUNUO0lBQ0Y7SUFFQSxNQUFNaUIsbUJBQW1CLE9BQU9uQixJQUFZQztRQUMxQyxJQUFJO1lBQ0ZWLFNBQVM7WUFDVCxNQUFNNkIsV0FBVyxNQUFNQyxNQUFNLHFCQUF3QixPQUFIckIsS0FBTTtnQkFDdERzQixRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkIsR0FBR3pCLElBQUk7b0JBQ1AwQixPQUFPOUM7Z0JBQ1Q7WUFDRjtZQUVBLE1BQU0rQyxTQUFTLE1BQU1SLFNBQVNTLElBQUk7WUFFbEMsSUFBSSxDQUFDVCxTQUFTVSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSXJDLE1BQU1tQyxPQUFPdEMsS0FBSyxJQUFJO1lBQ2xDO1lBRUEsSUFBSXNDLE9BQU9HLE9BQU8sRUFBRTtnQkFDbEIseUJBQXlCO2dCQUN6QjVDLGVBQWUrQixDQUFBQSxPQUNiQSxLQUFLYyxHQUFHLENBQUNDLENBQUFBLFNBQ1BBLE9BQU9qQyxFQUFFLEtBQUtBLEtBQUs0QixPQUFPM0IsSUFBSSxHQUFHZ0M7Z0JBR3JDLE9BQU9MLE9BQU8zQixJQUFJO1lBQ3BCLE9BQU87Z0JBQ0wsTUFBTSxJQUFJUixNQUFNbUMsT0FBT3RDLEtBQUssSUFBSTtZQUNsQztRQUNGLEVBQUUsT0FBT1ksS0FBSztZQUNaLE1BQU1DLGVBQWVELGVBQWVULFFBQVFTLElBQUlFLE9BQU8sR0FBRztZQUMxRGIsU0FBU1k7WUFDVEUsUUFBUWYsS0FBSyxDQUFDLCtCQUErQlk7WUFDN0MsT0FBTztRQUNUO0lBQ0Y7SUFFQSxNQUFNZ0MscUJBQXFCLE9BQU9sQyxJQUFZVztRQUM1QyxPQUFPUSxpQkFBaUJuQixJQUFJO1lBQUVXO1FBQWE7SUFDN0M7SUFFQSxNQUFNd0IsbUJBQW1CLE9BQU9uQztRQUM5QixJQUFJO1lBQ0ZULFNBQVM7WUFDVCxNQUFNNkIsV0FBVyxNQUFNQyxNQUFNLHFCQUFpQ3hDLE9BQVptQixJQUFHLFdBQXdCLE9BQWZuQixpQkFBa0I7Z0JBQzlFeUMsUUFBUTtZQUNWO1lBRUEsTUFBTU0sU0FBUyxNQUFNUixTQUFTUyxJQUFJO1lBRWxDLElBQUksQ0FBQ1QsU0FBU1UsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlyQyxNQUFNbUMsT0FBT3RDLEtBQUssSUFBSTtZQUNsQztZQUVBLElBQUlzQyxPQUFPRyxPQUFPLEVBQUU7Z0JBQ2xCLDBCQUEwQjtnQkFDMUI1QyxlQUFlK0IsQ0FBQUEsT0FBUUEsS0FBS2tCLE1BQU0sQ0FBQ0gsQ0FBQUEsU0FBVUEsT0FBT2pDLEVBQUUsS0FBS0E7Z0JBQzNELE9BQU87WUFDVCxPQUFPO2dCQUNMLE1BQU0sSUFBSVAsTUFBTW1DLE9BQU90QyxLQUFLLElBQUk7WUFDbEM7UUFDRixFQUFFLE9BQU9ZLEtBQUs7WUFDWixNQUFNQyxlQUFlRCxlQUFlVCxRQUFRUyxJQUFJRSxPQUFPLEdBQUc7WUFDMURiLFNBQVNZO1lBQ1RFLFFBQVFmLEtBQUssQ0FBQywrQkFBK0JZO1lBQzdDLE9BQU87UUFDVDtJQUNGO0lBRUEsZ0JBQWdCO0lBQ2hCaEMsZ0RBQVNBO29DQUFDO1lBQ1JzQjtRQUNGO21DQUFHO1FBQUNYO1FBQWdCRztRQUFRQztLQUFXO0lBRXZDLGVBQWU7SUFDZmYsZ0RBQVNBO29DQUFDO1lBQ1IsSUFBSSxDQUFDWSxhQUFhO1lBRWxCLE1BQU11RCxXQUFXQyxZQUFZOUMsa0JBQWtCVDtZQUMvQzs0Q0FBTyxJQUFNd0QsY0FBY0Y7O1FBQzdCO21DQUFHO1FBQUN2RDtRQUFhQztRQUFpQkY7UUFBZ0JHO1FBQVFDO0tBQVc7SUFFckUsT0FBTztRQUNMQztRQUNBRTtRQUNBRTtRQUNBa0QsU0FBU2hEO1FBQ1RjO1FBQ0FhO1FBQ0FnQjtRQUNBRDtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9jZ3JhbnQvZGV2L2RpZ2dpdC13ZWIvbGliL2hvb2tzL3VzZVRlYW1NZW1iZXJzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFRlYW1NZW1iZXIgfSBmcm9tICdAL2xpYi90eXBlcy9maXJlc3RvcmUnXG5pbXBvcnQgeyBkYiB9IGZyb20gJ0AvbGliL2ZpcmViYXNlL2NvbmZpZydcbmltcG9ydCB7XG4gIGNvbGxlY3Rpb24sXG4gIHF1ZXJ5LFxuICB3aGVyZSxcbiAgZ2V0RG9jcyxcbiAgYWRkRG9jLFxuICB1cGRhdGVEb2MsXG4gIGRlbGV0ZURvYyxcbiAgZG9jLFxuICBvcmRlckJ5LFxuICBUaW1lc3RhbXBcbn0gZnJvbSAnZmlyZWJhc2UvZmlyZXN0b3JlJ1xuXG5pbnRlcmZhY2UgVXNlVGVhbU1lbWJlcnNPcHRpb25zIHtcbiAgb3JnYW5pemF0aW9uSWQ/OiBzdHJpbmdcbiAgYXV0b1JlZnJlc2g/OiBib29sZWFuXG4gIHJlZnJlc2hJbnRlcnZhbD86IG51bWJlclxuICBzdGF0dXM/OiBzdHJpbmdcbiAgZGVwYXJ0bWVudD86IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgVXNlVGVhbU1lbWJlcnNSZXR1cm4ge1xuICB0ZWFtTWVtYmVyczogVGVhbU1lbWJlcltdXG4gIGxvYWRpbmc6IGJvb2xlYW5cbiAgZXJyb3I6IHN0cmluZyB8IG51bGxcbiAgcmVmZXRjaDogKCkgPT4gUHJvbWlzZTx2b2lkPlxuICBjcmVhdGVUZWFtTWVtYmVyOiAoZGF0YTogUGFydGlhbDxUZWFtTWVtYmVyPikgPT4gUHJvbWlzZTxUZWFtTWVtYmVyIHwgbnVsbD5cbiAgdXBkYXRlVGVhbU1lbWJlcjogKGlkOiBzdHJpbmcsIGRhdGE6IFBhcnRpYWw8VGVhbU1lbWJlcj4pID0+IFByb21pc2U8VGVhbU1lbWJlciB8IG51bGw+XG4gIGRlbGV0ZVRlYW1NZW1iZXI6IChpZDogc3RyaW5nKSA9PiBQcm9taXNlPGJvb2xlYW4+XG4gIHVwZGF0ZUF2YWlsYWJpbGl0eTogKGlkOiBzdHJpbmcsIGF2YWlsYWJpbGl0eTogUGFydGlhbDxUZWFtTWVtYmVyWydhdmFpbGFiaWxpdHknXT4pID0+IFByb21pc2U8VGVhbU1lbWJlciB8IG51bGw+XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VUZWFtTWVtYmVycyhvcHRpb25zOiBVc2VUZWFtTWVtYmVyc09wdGlvbnMgPSB7fSk6IFVzZVRlYW1NZW1iZXJzUmV0dXJuIHtcbiAgY29uc3Qge1xuICAgIG9yZ2FuaXphdGlvbklkID0gJ2RlbW8tb3JnJyxcbiAgICBhdXRvUmVmcmVzaCA9IGZhbHNlLFxuICAgIHJlZnJlc2hJbnRlcnZhbCA9IDMwMDAwLCAvLyAzMCBzZWNvbmRzXG4gICAgc3RhdHVzLFxuICAgIGRlcGFydG1lbnRcbiAgfSA9IG9wdGlvbnNcblxuICBjb25zdCBbdGVhbU1lbWJlcnMsIHNldFRlYW1NZW1iZXJzXSA9IHVzZVN0YXRlPFRlYW1NZW1iZXJbXT4oW10pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcblxuICBjb25zdCBmZXRjaFRlYW1NZW1iZXJzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRFcnJvcihudWxsKVxuXG4gICAgICBpZiAoIWRiKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmlyZXN0b3JlIG5vdCBpbml0aWFsaXplZCcpXG4gICAgICB9XG5cbiAgICAgIC8vIEJ1aWxkIHF1ZXJ5XG4gICAgICBsZXQgcSA9IHF1ZXJ5KFxuICAgICAgICBjb2xsZWN0aW9uKGRiLCAnb3JnYW5pemF0aW9ucycsIG9yZ2FuaXphdGlvbklkLCAndGVhbU1lbWJlcnMnKSxcbiAgICAgICAgb3JkZXJCeSgncHJvZmlsZS5maXJzdE5hbWUnLCAnYXNjJylcbiAgICAgIClcblxuICAgICAgLy8gQWRkIGZpbHRlcnMgaWYgcHJvdmlkZWRcbiAgICAgIGlmIChzdGF0dXMpIHtcbiAgICAgICAgcSA9IHF1ZXJ5KHEsIHdoZXJlKCdhdmFpbGFiaWxpdHkuc3RhdHVzJywgJz09Jywgc3RhdHVzKSlcbiAgICAgIH1cbiAgICAgIGlmIChkZXBhcnRtZW50KSB7XG4gICAgICAgIHEgPSBxdWVyeShxLCB3aGVyZSgnZGVwYXJ0bWVudCcsICc9PScsIGRlcGFydG1lbnQpKVxuICAgICAgfVxuXG4gICAgICBjb25zdCBxdWVyeVNuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhxKVxuICAgICAgY29uc3QgbWVtYmVyczogVGVhbU1lbWJlcltdID0gW11cblxuICAgICAgcXVlcnlTbmFwc2hvdC5mb3JFYWNoKChkb2MpID0+IHtcbiAgICAgICAgbWVtYmVycy5wdXNoKHtcbiAgICAgICAgICBpZDogZG9jLmlkLFxuICAgICAgICAgIC4uLmRvYy5kYXRhKClcbiAgICAgICAgfSBhcyBUZWFtTWVtYmVyKVxuICAgICAgfSlcblxuICAgICAgc2V0VGVhbU1lbWJlcnMobWVtYmVycylcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnQW4gZXJyb3Igb2NjdXJyZWQnXG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyB0ZWFtIG1lbWJlcnM6JywgZXJyKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGNyZWF0ZVRlYW1NZW1iZXIgPSBhc3luYyAoZGF0YTogUGFydGlhbDxUZWFtTWVtYmVyPik6IFByb21pc2U8VGVhbU1lbWJlciB8IG51bGw+ID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0RXJyb3IobnVsbClcblxuICAgICAgaWYgKCFkYikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZpcmVzdG9yZSBub3QgaW5pdGlhbGl6ZWQnKVxuICAgICAgfVxuXG4gICAgICAvLyBQcmVwYXJlIHRoZSBkYXRhIHdpdGggdGltZXN0YW1wc1xuICAgICAgY29uc3QgdGVhbU1lbWJlckRhdGEgPSB7XG4gICAgICAgIC4uLmRhdGEsXG4gICAgICAgIGNyZWF0ZWRBdDogVGltZXN0YW1wLm5vdygpLFxuICAgICAgICB1cGRhdGVkQXQ6IFRpbWVzdGFtcC5ub3coKSxcbiAgICAgICAgLy8gRW5zdXJlIHJlcXVpcmVkIGZpZWxkcyBoYXZlIGRlZmF1bHRzXG4gICAgICAgIGF2YWlsYWJpbGl0eToge1xuICAgICAgICAgIHN0YXR1czogJ2F2YWlsYWJsZScsXG4gICAgICAgICAgLi4uZGF0YS5hdmFpbGFiaWxpdHlcbiAgICAgICAgfSxcbiAgICAgICAgc2tpbGxzOiBkYXRhLnNraWxscyB8fCB7fSxcbiAgICAgICAgcHJvZmlsZToge1xuICAgICAgICAgIGZpcnN0TmFtZTogJycsXG4gICAgICAgICAgbGFzdE5hbWU6ICcnLFxuICAgICAgICAgIC4uLmRhdGEucHJvZmlsZVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRvY1JlZiA9IGF3YWl0IGFkZERvYyhcbiAgICAgICAgY29sbGVjdGlvbihkYiwgJ29yZ2FuaXphdGlvbnMnLCBvcmdhbml6YXRpb25JZCwgJ3RlYW1NZW1iZXJzJyksXG4gICAgICAgIHRlYW1NZW1iZXJEYXRhXG4gICAgICApXG5cbiAgICAgIGNvbnN0IG5ld01lbWJlciA9IHtcbiAgICAgICAgaWQ6IGRvY1JlZi5pZCxcbiAgICAgICAgLi4udGVhbU1lbWJlckRhdGFcbiAgICAgIH0gYXMgVGVhbU1lbWJlclxuXG4gICAgICAvLyBBZGQgdG8gbG9jYWwgc3RhdGVcbiAgICAgIHNldFRlYW1NZW1iZXJzKHByZXYgPT4gWy4uLnByZXYsIG5ld01lbWJlcl0pXG5cbiAgICAgIHJldHVybiBuZXdNZW1iZXJcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnQW4gZXJyb3Igb2NjdXJyZWQnXG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyB0ZWFtIG1lbWJlcjonLCBlcnIpXG4gICAgICByZXR1cm4gbnVsbFxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHVwZGF0ZVRlYW1NZW1iZXIgPSBhc3luYyAoaWQ6IHN0cmluZywgZGF0YTogUGFydGlhbDxUZWFtTWVtYmVyPik6IFByb21pc2U8VGVhbU1lbWJlciB8IG51bGw+ID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0RXJyb3IobnVsbClcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvdGVhbS1tZW1iZXJzLyR7aWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdQQVRDSCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgLi4uZGF0YSxcbiAgICAgICAgICBvcmdJZDogb3JnYW5pemF0aW9uSWRcbiAgICAgICAgfSksXG4gICAgICB9KVxuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKClcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0LmVycm9yIHx8ICdGYWlsZWQgdG8gdXBkYXRlIHRlYW0gbWVtYmVyJylcbiAgICAgIH1cblxuICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICAgIC8vIFVwZGF0ZSB0aGUgbG9jYWwgc3RhdGVcbiAgICAgICAgc2V0VGVhbU1lbWJlcnMocHJldiA9PiBcbiAgICAgICAgICBwcmV2Lm1hcChtZW1iZXIgPT4gXG4gICAgICAgICAgICBtZW1iZXIuaWQgPT09IGlkID8gcmVzdWx0LmRhdGEgOiBtZW1iZXJcbiAgICAgICAgICApXG4gICAgICAgIClcbiAgICAgICAgcmV0dXJuIHJlc3VsdC5kYXRhXG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0LmVycm9yIHx8ICdGYWlsZWQgdG8gdXBkYXRlIHRlYW0gbWVtYmVyJylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnQW4gZXJyb3Igb2NjdXJyZWQnXG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyB0ZWFtIG1lbWJlcjonLCBlcnIpXG4gICAgICByZXR1cm4gbnVsbFxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHVwZGF0ZUF2YWlsYWJpbGl0eSA9IGFzeW5jIChpZDogc3RyaW5nLCBhdmFpbGFiaWxpdHk6IFBhcnRpYWw8VGVhbU1lbWJlclsnYXZhaWxhYmlsaXR5J10+KTogUHJvbWlzZTxUZWFtTWVtYmVyIHwgbnVsbD4gPT4ge1xuICAgIHJldHVybiB1cGRhdGVUZWFtTWVtYmVyKGlkLCB7IGF2YWlsYWJpbGl0eSB9KVxuICB9XG5cbiAgY29uc3QgZGVsZXRlVGVhbU1lbWJlciA9IGFzeW5jIChpZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldEVycm9yKG51bGwpXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3RlYW0tbWVtYmVycy8ke2lkfT9vcmdJZD0ke29yZ2FuaXphdGlvbklkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcbiAgICAgIH0pXG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihyZXN1bHQuZXJyb3IgfHwgJ0ZhaWxlZCB0byBkZWxldGUgdGVhbSBtZW1iZXInKVxuICAgICAgfVxuXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgLy8gUmVtb3ZlIGZyb20gbG9jYWwgc3RhdGVcbiAgICAgICAgc2V0VGVhbU1lbWJlcnMocHJldiA9PiBwcmV2LmZpbHRlcihtZW1iZXIgPT4gbWVtYmVyLmlkICE9PSBpZCkpXG4gICAgICAgIHJldHVybiB0cnVlXG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0LmVycm9yIHx8ICdGYWlsZWQgdG8gZGVsZXRlIHRlYW0gbWVtYmVyJylcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnQW4gZXJyb3Igb2NjdXJyZWQnXG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyB0ZWFtIG1lbWJlcjonLCBlcnIpXG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cblxuICAvLyBJbml0aWFsIGZldGNoXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hUZWFtTWVtYmVycygpXG4gIH0sIFtvcmdhbml6YXRpb25JZCwgc3RhdHVzLCBkZXBhcnRtZW50XSlcblxuICAvLyBBdXRvLXJlZnJlc2hcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWF1dG9SZWZyZXNoKSByZXR1cm5cblxuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoZmV0Y2hUZWFtTWVtYmVycywgcmVmcmVzaEludGVydmFsKVxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKVxuICB9LCBbYXV0b1JlZnJlc2gsIHJlZnJlc2hJbnRlcnZhbCwgb3JnYW5pemF0aW9uSWQsIHN0YXR1cywgZGVwYXJ0bWVudF0pXG5cbiAgcmV0dXJuIHtcbiAgICB0ZWFtTWVtYmVycyxcbiAgICBsb2FkaW5nLFxuICAgIGVycm9yLFxuICAgIHJlZmV0Y2g6IGZldGNoVGVhbU1lbWJlcnMsXG4gICAgY3JlYXRlVGVhbU1lbWJlcixcbiAgICB1cGRhdGVUZWFtTWVtYmVyLFxuICAgIGRlbGV0ZVRlYW1NZW1iZXIsXG4gICAgdXBkYXRlQXZhaWxhYmlsaXR5XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImRiIiwiY29sbGVjdGlvbiIsInF1ZXJ5Iiwid2hlcmUiLCJnZXREb2NzIiwiYWRkRG9jIiwib3JkZXJCeSIsIlRpbWVzdGFtcCIsInVzZVRlYW1NZW1iZXJzIiwib3B0aW9ucyIsIm9yZ2FuaXphdGlvbklkIiwiYXV0b1JlZnJlc2giLCJyZWZyZXNoSW50ZXJ2YWwiLCJzdGF0dXMiLCJkZXBhcnRtZW50IiwidGVhbU1lbWJlcnMiLCJzZXRUZWFtTWVtYmVycyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImZldGNoVGVhbU1lbWJlcnMiLCJFcnJvciIsInEiLCJxdWVyeVNuYXBzaG90IiwibWVtYmVycyIsImZvckVhY2giLCJkb2MiLCJwdXNoIiwiaWQiLCJkYXRhIiwiZXJyIiwiZXJyb3JNZXNzYWdlIiwibWVzc2FnZSIsImNvbnNvbGUiLCJjcmVhdGVUZWFtTWVtYmVyIiwidGVhbU1lbWJlckRhdGEiLCJjcmVhdGVkQXQiLCJub3ciLCJ1cGRhdGVkQXQiLCJhdmFpbGFiaWxpdHkiLCJza2lsbHMiLCJwcm9maWxlIiwiZmlyc3ROYW1lIiwibGFzdE5hbWUiLCJkb2NSZWYiLCJuZXdNZW1iZXIiLCJwcmV2IiwidXBkYXRlVGVhbU1lbWJlciIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJvcmdJZCIsInJlc3VsdCIsImpzb24iLCJvayIsInN1Y2Nlc3MiLCJtYXAiLCJtZW1iZXIiLCJ1cGRhdGVBdmFpbGFiaWxpdHkiLCJkZWxldGVUZWFtTWVtYmVyIiwiZmlsdGVyIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImNsZWFySW50ZXJ2YWwiLCJyZWZldGNoIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/hooks/useTeamMembers.ts\n"));

/***/ })

});