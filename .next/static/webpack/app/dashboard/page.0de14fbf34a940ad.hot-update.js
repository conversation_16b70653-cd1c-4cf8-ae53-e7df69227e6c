"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./lib/hooks/useTeamMembers.ts":
/*!*************************************!*\
  !*** ./lib/hooks/useTeamMembers.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTeamMembers: () => (/* binding */ useTeamMembers)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTeamMembers auto */ \nfunction useTeamMembers() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { organizationId = 'demo-org', autoRefresh = false, refreshInterval = 30000, status, department } = options;\n    const [teamMembers, setTeamMembers] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchTeamMembers = async ()=>{\n        try {\n            setError(null);\n            const params = new URLSearchParams({\n                orgId: organizationId\n            });\n            if (status) params.append('status', status);\n            if (department) params.append('department', department);\n            const response = await fetch(\"/api/team-members?\".concat(params));\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to fetch team members');\n            }\n            if (result.success) {\n                setTeamMembers(result.data || []);\n            } else {\n                throw new Error(result.error || 'Failed to fetch team members');\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error fetching team members:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createTeamMember = async (data)=>{\n        try {\n            setError(null);\n            const response = await fetch('/api/team-members', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...data,\n                    orgId: organizationId\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to create team member');\n            }\n            if (result.success) {\n                // Refresh the list to include the new member\n                await fetchTeamMembers();\n                return result.data;\n            } else {\n                throw new Error(result.error || 'Failed to create team member');\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error creating team member:', err);\n            return null;\n        }\n    };\n    const updateTeamMember = async (id, data)=>{\n        try {\n            setError(null);\n            const response = await fetch(\"/api/team-members/\".concat(id), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...data,\n                    orgId: organizationId\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to update team member');\n            }\n            if (result.success) {\n                // Update the local state\n                setTeamMembers((prev)=>prev.map((member)=>member.id === id ? result.data : member));\n                return result.data;\n            } else {\n                throw new Error(result.error || 'Failed to update team member');\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error updating team member:', err);\n            return null;\n        }\n    };\n    const updateAvailability = async (id, availability)=>{\n        return updateTeamMember(id, {\n            availability\n        });\n    };\n    const deleteTeamMember = async (id)=>{\n        try {\n            setError(null);\n            const response = await fetch(\"/api/team-members/\".concat(id, \"?orgId=\").concat(organizationId), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to delete team member');\n            }\n            if (result.success) {\n                // Remove from local state\n                setTeamMembers((prev)=>prev.filter((member)=>member.id !== id));\n                return true;\n            } else {\n                throw new Error(result.error || 'Failed to delete team member');\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error deleting team member:', err);\n            return false;\n        }\n    };\n    // Initial fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTeamMembers.useEffect\": ()=>{\n            fetchTeamMembers();\n        }\n    }[\"useTeamMembers.useEffect\"], [\n        organizationId,\n        status,\n        department\n    ]);\n    // Auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTeamMembers.useEffect\": ()=>{\n            if (!autoRefresh) return;\n            const interval = setInterval(fetchTeamMembers, refreshInterval);\n            return ({\n                \"useTeamMembers.useEffect\": ()=>clearInterval(interval)\n            })[\"useTeamMembers.useEffect\"];\n        }\n    }[\"useTeamMembers.useEffect\"], [\n        autoRefresh,\n        refreshInterval,\n        organizationId,\n        status,\n        department\n    ]);\n    return {\n        teamMembers,\n        loading,\n        error,\n        refetch: fetchTeamMembers,\n        createTeamMember,\n        updateTeamMember,\n        deleteTeamMember,\n        updateAvailability\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/hooks/useTeamMembers.ts\n"));

/***/ })

});