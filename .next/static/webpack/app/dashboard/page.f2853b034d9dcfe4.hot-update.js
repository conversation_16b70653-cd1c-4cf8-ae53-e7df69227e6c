"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./lib/hooks/useTeamMembers.ts":
/*!*************************************!*\
  !*** ./lib/hooks/useTeamMembers.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTeamMembers: () => (/* binding */ useTeamMembers)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase/config */ \"(app-pages-browser)/./lib/firebase/config.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ useTeamMembers auto */ \n\n\nfunction useTeamMembers() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { organizationId = 'demo-org', autoRefresh = false, refreshInterval = 30000, status, department } = options;\n    const [teamMembers, setTeamMembers] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchTeamMembers = async ()=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            // Build query\n            let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)('profile.firstName', 'asc'));\n            // Add filters if provided\n            if (status) {\n                q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('availability.status', '==', status));\n            }\n            if (department) {\n                q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('department', '==', department));\n            }\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            const members = [];\n            querySnapshot.forEach((doc)=>{\n                members.push({\n                    id: doc.id,\n                    ...doc.data()\n                });\n            });\n            setTeamMembers(members);\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error fetching team members:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createTeamMember = async (data)=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            // Prepare the data with timestamps\n            const teamMemberData = {\n                ...data,\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now(),\n                // Ensure required fields have defaults\n                availability: {\n                    status: 'available',\n                    ...data.availability\n                },\n                skills: data.skills || {},\n                profile: {\n                    firstName: '',\n                    lastName: '',\n                    ...data.profile\n                }\n            };\n            const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers'), teamMemberData);\n            const newMember = {\n                id: docRef.id,\n                ...teamMemberData\n            };\n            // Add to local state\n            setTeamMembers((prev)=>[\n                    ...prev,\n                    newMember\n                ]);\n            return newMember;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error creating team member:', err);\n            return null;\n        }\n    };\n    const updateTeamMember = async (id, data)=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            // Prepare update data with timestamp\n            const updateData = {\n                ...data,\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now()\n            };\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers', id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(docRef, updateData);\n            // Update local state\n            setTeamMembers((prev)=>prev.map((member)=>member.id === id ? {\n                        ...member,\n                        ...updateData\n                    } : member));\n            const updatedMember = teamMembers.find((m)=>m.id === id);\n            return updatedMember ? {\n                ...updatedMember,\n                ...updateData\n            } : null;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error updating team member:', err);\n            return null;\n        }\n    };\n    const updateAvailability = async (id, availability)=>{\n        return updateTeamMember(id, {\n            availability\n        });\n    };\n    const deleteTeamMember = async (id)=>{\n        try {\n            setError(null);\n            const response = await fetch(\"/api/team-members/\".concat(id, \"?orgId=\").concat(organizationId), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to delete team member');\n            }\n            if (result.success) {\n                // Remove from local state\n                setTeamMembers((prev)=>prev.filter((member)=>member.id !== id));\n                return true;\n            } else {\n                throw new Error(result.error || 'Failed to delete team member');\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error deleting team member:', err);\n            return false;\n        }\n    };\n    // Initial fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTeamMembers.useEffect\": ()=>{\n            fetchTeamMembers();\n        }\n    }[\"useTeamMembers.useEffect\"], [\n        organizationId,\n        status,\n        department\n    ]);\n    // Auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTeamMembers.useEffect\": ()=>{\n            if (!autoRefresh) return;\n            const interval = setInterval(fetchTeamMembers, refreshInterval);\n            return ({\n                \"useTeamMembers.useEffect\": ()=>clearInterval(interval)\n            })[\"useTeamMembers.useEffect\"];\n        }\n    }[\"useTeamMembers.useEffect\"], [\n        autoRefresh,\n        refreshInterval,\n        organizationId,\n        status,\n        department\n    ]);\n    return {\n        teamMembers,\n        loading,\n        error,\n        refetch: fetchTeamMembers,\n        createTeamMember,\n        updateTeamMember,\n        deleteTeamMember,\n        updateAvailability\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/hooks/useTeamMembers.ts\n"));

/***/ })

});