"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./lib/hooks/useTeamMembers.ts":
/*!*************************************!*\
  !*** ./lib/hooks/useTeamMembers.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTeamMembers: () => (/* binding */ useTeamMembers)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase/config */ \"(app-pages-browser)/./lib/firebase/config.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ useTeamMembers auto */ \n\n\nfunction useTeamMembers() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { organizationId = 'demo-org', autoRefresh = false, refreshInterval = 30000, status, department } = options;\n    const [teamMembers, setTeamMembers] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const fetchTeamMembers = async ()=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            // Build query\n            let q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)('profile.firstName', 'asc'));\n            // Add filters if provided\n            if (status) {\n                q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('availability.status', '==', status));\n            }\n            if (department) {\n                q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(q, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)('department', '==', department));\n            }\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            const members = [];\n            querySnapshot.forEach((doc)=>{\n                members.push({\n                    id: doc.id,\n                    ...doc.data()\n                });\n            });\n            setTeamMembers(members);\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error fetching team members:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createTeamMember = async (data)=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            // Prepare the data with timestamps\n            const teamMemberData = {\n                ...data,\n                createdAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now(),\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now(),\n                // Ensure required fields have defaults\n                availability: {\n                    status: 'available',\n                    ...data.availability\n                },\n                skills: data.skills || {},\n                profile: {\n                    firstName: '',\n                    lastName: '',\n                    ...data.profile\n                }\n            };\n            const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.addDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers'), teamMemberData);\n            const newMember = {\n                id: docRef.id,\n                ...teamMemberData\n            };\n            // Add to local state\n            setTeamMembers((prev)=>[\n                    ...prev,\n                    newMember\n                ]);\n            return newMember;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error creating team member:', err);\n            return null;\n        }\n    };\n    const updateTeamMember = async (id, data)=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            // Prepare update data with timestamp\n            const updateData = {\n                ...data,\n                updatedAt: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now()\n            };\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers', id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(docRef, updateData);\n            // Update local state\n            setTeamMembers((prev)=>prev.map((member)=>member.id === id ? {\n                        ...member,\n                        ...updateData\n                    } : member));\n            const updatedMember = teamMembers.find((m)=>m.id === id);\n            return updatedMember ? {\n                ...updatedMember,\n                ...updateData\n            } : null;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error updating team member:', err);\n            return null;\n        }\n    };\n    const updateAvailability = async (id, availability)=>{\n        return updateTeamMember(id, {\n            availability\n        });\n    };\n    const deleteTeamMember = async (id)=>{\n        try {\n            setError(null);\n            if (!_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db) {\n                throw new Error('Firestore not initialized');\n            }\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase_config__WEBPACK_IMPORTED_MODULE_1__.db, 'organizations', organizationId, 'teamMembers', id);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)(docRef);\n            // Remove from local state\n            setTeamMembers((prev)=>prev.filter((member)=>member.id !== id));\n            return true;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n            setError(errorMessage);\n            console.error('Error deleting team member:', err);\n            return false;\n        }\n    };\n    // Initial fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTeamMembers.useEffect\": ()=>{\n            fetchTeamMembers();\n        }\n    }[\"useTeamMembers.useEffect\"], [\n        organizationId,\n        status,\n        department\n    ]);\n    // Auto-refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTeamMembers.useEffect\": ()=>{\n            if (!autoRefresh) return;\n            const interval = setInterval(fetchTeamMembers, refreshInterval);\n            return ({\n                \"useTeamMembers.useEffect\": ()=>clearInterval(interval)\n            })[\"useTeamMembers.useEffect\"];\n        }\n    }[\"useTeamMembers.useEffect\"], [\n        autoRefresh,\n        refreshInterval,\n        organizationId,\n        status,\n        department\n    ]);\n    return {\n        teamMembers,\n        loading,\n        error,\n        refetch: fetchTeamMembers,\n        createTeamMember,\n        updateTeamMember,\n        deleteTeamMember,\n        updateAvailability\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/hooks/useTeamMembers.ts\n"));

/***/ })

});