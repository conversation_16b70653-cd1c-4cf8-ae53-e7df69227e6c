/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/team-members/route";
exports.ids = ["app/api/team-members/route"];
exports.modules = {

/***/ "(rsc)/./app/api/team-members/route.ts":
/*!***************************************!*\
  !*** ./app/api/team-members/route.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase/admin */ \"(rsc)/./lib/firebase/admin.ts\");\n/* harmony import */ var firebase_admin_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase-admin/firestore */ \"firebase-admin/firestore\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__, firebase_admin_firestore__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__, firebase_admin_firestore__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// GET /api/team-members - List all team members for an organization\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const orgId = searchParams.get('orgId') || 'demo-org';\n        const status = searchParams.get('status') // Filter by status\n        ;\n        const department = searchParams.get('department') // Filter by department\n        ;\n        const app = (0,_lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.getFirebaseAdmin)();\n        const db = (0,firebase_admin_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n        console.log(`👥 Fetching team members for organization: ${orgId}`);\n        let query = db.collection('organizations').doc(orgId).collection('teamMembers').orderBy('createdAt', 'desc');\n        // Apply filters if provided\n        if (status) {\n            query = query.where('status', '==', status);\n        }\n        if (department) {\n            query = query.where('department', '==', department);\n        }\n        const teamMembersSnapshot = await query.get();\n        const teamMembers = teamMembersSnapshot.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data()\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: teamMembers,\n            count: teamMembers.length,\n            organizationId: orgId,\n            filters: {\n                status,\n                department\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('❌ Error fetching team members:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : 'Failed to fetch team members',\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/team-members - Create a new team member\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { orgId = 'demo-org', ...teamMemberData } = body;\n        const app = (0,_lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.getFirebaseAdmin)();\n        const db = (0,firebase_admin_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n        console.log(`👥 Creating new team member for organization: ${orgId}`);\n        // Validate required fields\n        const requiredFields = [\n            'firstName',\n            'lastName',\n            'position',\n            'department'\n        ];\n        for (const field of requiredFields){\n            if (!teamMemberData[field]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: `Missing required field: ${field}`,\n                    timestamp: new Date().toISOString()\n                }, {\n                    status: 400\n                });\n            }\n        }\n        const now = firebase_admin_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.now();\n        const newTeamMember = {\n            ...teamMemberData,\n            organizationId: orgId,\n            status: teamMemberData.status || 'active',\n            hireDate: teamMemberData.hireDate ? firebase_admin_firestore__WEBPACK_IMPORTED_MODULE_2__.Timestamp.fromDate(new Date(teamMemberData.hireDate)) : now,\n            availability: {\n                status: 'available',\n                lastUpdated: now,\n                ...teamMemberData.availability\n            },\n            skills: teamMemberData.skills || {},\n            createdAt: now,\n            updatedAt: now,\n            createdBy: 'api'\n        };\n        const docRef = await db.collection('organizations').doc(orgId).collection('teamMembers').add(newTeamMember);\n        const createdTeamMember = {\n            id: docRef.id,\n            ...newTeamMember\n        };\n        console.log(`✅ Team member created successfully: ${docRef.id}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: createdTeamMember,\n            message: 'Team member created successfully',\n            timestamp: new Date().toISOString()\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('❌ Error creating team member:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : 'Failed to create team member',\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/team-members/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/firebase/admin.ts":
/*!*******************************!*\
  !*** ./lib/firebase/admin.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminDb: () => (/* binding */ adminDb),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getFirebaseAdmin: () => (/* binding */ getFirebaseAdmin)\n/* harmony export */ });\n/* harmony import */ var firebase_admin_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase-admin/app */ \"firebase-admin/app\");\n/* harmony import */ var firebase_admin_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase-admin/firestore */ \"firebase-admin/firestore\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_admin_app__WEBPACK_IMPORTED_MODULE_0__, firebase_admin_firestore__WEBPACK_IMPORTED_MODULE_1__]);\n([firebase_admin_app__WEBPACK_IMPORTED_MODULE_0__, firebase_admin_firestore__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n// Check if Firebase credentials are available\nconst hasFirebaseCredentials = !!(process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_CLIENT_EMAIL && process.env.FIREBASE_PRIVATE_KEY);\nconst firebaseAdminConfig = hasFirebaseCredentials ? {\n    credential: (0,firebase_admin_app__WEBPACK_IMPORTED_MODULE_0__.cert)({\n        projectId: process.env.FIREBASE_PROJECT_ID,\n        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,\n        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n')\n    })\n} : null;\nfunction createFirebaseAdminApp() {\n    if (!hasFirebaseCredentials) {\n        console.warn('Firebase credentials not found. Using mock data.');\n        return null;\n    }\n    if ((0,firebase_admin_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length > 0) {\n        return (0,firebase_admin_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n    }\n    return (0,firebase_admin_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseAdminConfig);\n}\nfunction getFirebaseAdmin() {\n    return createFirebaseAdminApp();\n}\nconst firebaseAdmin = createFirebaseAdminApp();\nconst adminDb = firebaseAdmin ? (0,firebase_admin_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)(firebaseAdmin) : null;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (firebaseAdmin);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/firebase/admin.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fteam-members%2Froute&page=%2Fapi%2Fteam-members%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteam-members%2Froute.ts&appDir=%2FUsers%2Fcgrant%2Fdev%2Fdiggit-web%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcgrant%2Fdev%2Fdiggit-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fteam-members%2Froute&page=%2Fapi%2Fteam-members%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteam-members%2Froute.ts&appDir=%2FUsers%2Fcgrant%2Fdev%2Fdiggit-web%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcgrant%2Fdev%2Fdiggit-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_cgrant_dev_diggit_web_app_api_team_members_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/team-members/route.ts */ \"(rsc)/./app/api/team-members/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Users_cgrant_dev_diggit_web_app_api_team_members_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_Users_cgrant_dev_diggit_web_app_api_team_members_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/team-members/route\",\n        pathname: \"/api/team-members\",\n        filename: \"route\",\n        bundlePath: \"app/api/team-members/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/dev/diggit-web/app/api/team-members/route.ts\",\n    nextConfigOutput,\n    userland: _Users_cgrant_dev_diggit_web_app_api_team_members_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fteam-members%2Froute&page=%2Fapi%2Fteam-members%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteam-members%2Froute.ts&appDir=%2FUsers%2Fcgrant%2Fdev%2Fdiggit-web%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcgrant%2Fdev%2Fdiggit-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "firebase-admin/app":
/*!*************************************!*\
  !*** external "firebase-admin/app" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase-admin/app");;

/***/ }),

/***/ "firebase-admin/firestore":
/*!*******************************************!*\
  !*** external "firebase-admin/firestore" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase-admin/firestore");;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fteam-members%2Froute&page=%2Fapi%2Fteam-members%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteam-members%2Froute.ts&appDir=%2FUsers%2Fcgrant%2Fdev%2Fdiggit-web%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fcgrant%2Fdev%2Fdiggit-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();