globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/equipment/[id]/route"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/app-sidebar.tsx":{"*":{"id":"(ssr)/./components/app-sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/top-navigation.tsx":{"*":{"id":"(ssr)/./components/top-navigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/sidebar.tsx":{"*":{"id":"(ssr)/./components/ui/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scheduling/availability/page.tsx":{"*":{"id":"(ssr)/./app/scheduling/availability/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scheduling/daily-board/page.tsx":{"*":{"id":"(ssr)/./app/scheduling/daily-board/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/fleet/page.tsx":{"*":{"id":"(ssr)/./app/fleet/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scheduling/emergency/page.tsx":{"*":{"id":"(ssr)/./app/scheduling/emergency/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scheduling/resource-matching/page.tsx":{"*":{"id":"(ssr)/./app/scheduling/resource-matching/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/team-members/page.tsx":{"*":{"id":"(ssr)/./app/team-members/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/progress.tsx":{"*":{"id":"(ssr)/./components/ui/progress.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/dev/diggit-web/app/globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/dev/diggit-web/components/app-sidebar.tsx":{"id":"(app-pages-browser)/./components/app-sidebar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/dev/diggit-web/components/top-navigation.tsx":{"id":"(app-pages-browser)/./components/top-navigation.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/dev/diggit-web/components/ui/sidebar.tsx":{"id":"(app-pages-browser)/./components/ui/sidebar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/dev/diggit-web/components/ui/toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/dev/diggit-web/app/dashboard/page.tsx":{"id":"(app-pages-browser)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/dev/diggit-web/app/scheduling/availability/page.tsx":{"id":"(app-pages-browser)/./app/scheduling/availability/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/dev/diggit-web/app/scheduling/daily-board/page.tsx":{"id":"(app-pages-browser)/./app/scheduling/daily-board/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/dev/diggit-web/app/fleet/page.tsx":{"id":"(app-pages-browser)/./app/fleet/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx":{"id":"(app-pages-browser)/./app/scheduling/emergency/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/dev/diggit-web/app/scheduling/resource-matching/page.tsx":{"id":"(app-pages-browser)/./app/scheduling/resource-matching/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/dev/diggit-web/app/team-members/page.tsx":{"id":"(app-pages-browser)/./app/team-members/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/dev/diggit-web/components/ui/progress.tsx":{"id":"(app-pages-browser)/./components/ui/progress.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/dev/diggit-web/":[],"/Users/<USER>/dev/diggit-web/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/Users/<USER>/dev/diggit-web/app/page":[],"/Users/<USER>/dev/diggit-web/app/api/equipment/route":[],"/Users/<USER>/dev/diggit-web/app/api/equipment/[id]/route":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/app-sidebar.tsx":{"*":{"id":"(rsc)/./components/app-sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/top-navigation.tsx":{"*":{"id":"(rsc)/./components/top-navigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/sidebar.tsx":{"*":{"id":"(rsc)/./components/ui/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(rsc)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scheduling/availability/page.tsx":{"*":{"id":"(rsc)/./app/scheduling/availability/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scheduling/daily-board/page.tsx":{"*":{"id":"(rsc)/./app/scheduling/daily-board/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/fleet/page.tsx":{"*":{"id":"(rsc)/./app/fleet/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scheduling/emergency/page.tsx":{"*":{"id":"(rsc)/./app/scheduling/emergency/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scheduling/resource-matching/page.tsx":{"*":{"id":"(rsc)/./app/scheduling/resource-matching/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/team-members/page.tsx":{"*":{"id":"(rsc)/./app/team-members/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/progress.tsx":{"*":{"id":"(rsc)/./components/ui/progress.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}