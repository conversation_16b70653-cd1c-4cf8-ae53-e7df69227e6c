"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Equipment } from '@/lib/types/firestore'
import { getStatusColor, getStatusDot, getStatusDisplayText } from '@/lib/utils/equipment'
import { MapPin, Clock, Wrench, Calendar, Hash, FileText, Edit, Trash2 } from 'lucide-react'

interface EquipmentDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  equipment: Equipment | null
  onEdit: (equipment: Equipment) => void
  onDelete: (equipment: Equipment) => void
}

export function EquipmentDetailsModal({ 
  isOpen, 
  onClose, 
  equipment, 
  onEdit, 
  onDelete 
}: EquipmentDetailsModalProps) {
  if (!equipment) return null

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'Not set'
    try {
      return new Date(dateString).toLocaleDateString()
    } catch {
      return 'Invalid date'
    }
  }

  const getMaintenanceStatus = () => {
    if (!equipment.maintenance?.nextServiceDue) return 'No schedule'
    
    const nextService = new Date(equipment.maintenance.nextServiceDue)
    const today = new Date()
    const daysUntilService = Math.ceil((nextService.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysUntilService < 0) return 'Overdue'
    if (daysUntilService <= 7) return 'Due soon'
    return 'On schedule'
  }

  const getMaintenanceStatusColor = () => {
    const status = getMaintenanceStatus()
    switch (status) {
      case 'Overdue': return 'bg-red-100 text-red-800'
      case 'Due soon': return 'bg-yellow-100 text-yellow-800'
      case 'On schedule': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl">{equipment.name}</DialogTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => onEdit(equipment)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline" size="sm" onClick={() => onDelete(equipment)}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status and Basic Info */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${getStatusDot(equipment.status)}`}></div>
              <Badge variant="secondary" className={getStatusColor(equipment.status)}>
                {getStatusDisplayText(equipment.status)}
              </Badge>
            </div>
            <Badge variant="outline" className={getMaintenanceStatusColor()}>
              <Wrench className="h-3 w-3 mr-1" />
              {getMaintenanceStatus()}
            </Badge>
          </div>

          {/* Equipment Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Equipment Information</h3>
              
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-600 w-20">Type:</span>
                  <span>{equipment.type}</span>
                </div>
                
                {equipment.make && (
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-600 w-20">Make:</span>
                    <span>{equipment.make}</span>
                  </div>
                )}
                
                {equipment.model && (
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-600 w-20">Model:</span>
                    <span>{equipment.model}</span>
                  </div>
                )}
                
                {equipment.year && (
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-600 w-20">Year:</span>
                    <span>{equipment.year}</span>
                  </div>
                )}
                
                {equipment.serialNumber && (
                  <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4 text-gray-400" />
                    <span className="font-medium text-gray-600">Serial:</span>
                    <span className="font-mono text-sm">{equipment.serialNumber}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Location & Status</h3>
              
              <div className="space-y-3">
                {equipment.currentLocation?.address && (
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                    <div>
                      <span className="font-medium text-gray-600">Location:</span>
                      <p className="text-sm">{equipment.currentLocation.address}</p>
                    </div>
                  </div>
                )}
                
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="font-medium text-gray-600">Updated:</span>
                  <span className="text-sm">
                    {formatDate(equipment.updatedAt || equipment.createdAt)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Maintenance Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Wrench className="h-5 w-5" />
              Maintenance Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="text-sm font-medium text-gray-600">Current Hours</div>
                <div className="text-2xl font-bold">
                  {equipment.maintenance?.currentHours?.toLocaleString() || '0'}
                </div>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="text-sm font-medium text-gray-600">Service Interval</div>
                <div className="text-2xl font-bold">
                  {equipment.maintenance?.serviceInterval || 250}h
                </div>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="text-sm font-medium text-gray-600">Last Service</div>
                <div className="text-lg font-semibold">
                  {formatDate(equipment.maintenance?.lastServiceDate)}
                </div>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="text-sm font-medium text-gray-600">Next Service</div>
                <div className="text-lg font-semibold">
                  {formatDate(equipment.maintenance?.nextServiceDue)}
                </div>
              </div>
            </div>
          </div>

          {/* Description */}
          {equipment.description && (
            <>
              <Separator />
              <div className="space-y-3">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Description
                </h3>
                <p className="text-gray-700 leading-relaxed">{equipment.description}</p>
              </div>
            </>
          )}

          {/* Timestamps */}
          <Separator />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
            <div>
              <span className="font-medium">Created:</span> {formatDate(equipment.createdAt)}
            </div>
            {equipment.updatedAt && equipment.updatedAt !== equipment.createdAt && (
              <div>
                <span className="font-medium">Last Updated:</span> {formatDate(equipment.updatedAt)}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
