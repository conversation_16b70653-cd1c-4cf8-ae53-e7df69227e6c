"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { TeamMember, TeamMemberStatus, AvailabilityStatus, SkillLevel } from "@/lib/types/firestore"
import { X, Plus } from "lucide-react"

interface TeamMemberFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: Partial<TeamMember>) => Promise<void>
  teamMember?: TeamMember | null
  isLoading?: boolean
}

interface SkillEntry {
  id: string
  level: SkillLevel
  certificationNumber?: string
  expiresAt?: string
}

export function TeamMemberFormModal({
  isOpen,
  onClose,
  onSubmit,
  teamMember,
  isLoading = false
}: TeamMemberFormModalProps) {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    employeeId: '',
    position: '',
    department: '',
    hireDate: '',
    status: 'active' as TeamMemberStatus,
    availabilityStatus: 'available' as AvailabilityStatus,
    currentLocation: '',
    shiftStart: '',
    shiftEnd: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    notes: ''
  })

  const [skills, setSkills] = useState<SkillEntry[]>([])
  const [newSkillName, setNewSkillName] = useState('')

  // Populate form when editing
  useEffect(() => {
    if (teamMember) {
      setFormData({
        firstName: teamMember.firstName || '',
        lastName: teamMember.lastName || '',
        email: teamMember.email || '',
        phone: teamMember.phone || '',
        employeeId: teamMember.employeeId || '',
        position: teamMember.position || '',
        department: teamMember.department || '',
        hireDate: teamMember.hireDate ? new Date(teamMember.hireDate.seconds * 1000).toISOString().split('T')[0] : '',
        status: teamMember.status || 'active',
        availabilityStatus: teamMember.availability?.status || 'available',
        currentLocation: teamMember.availability?.currentLocation?.name || '',
        shiftStart: teamMember.availability?.shift?.start || '',
        shiftEnd: teamMember.availability?.shift?.end || '',
        emergencyContactName: teamMember.emergencyContact?.name || '',
        emergencyContactPhone: teamMember.emergencyContact?.phone || '',
        emergencyContactRelationship: teamMember.emergencyContact?.relationship || '',
        notes: teamMember.profile?.notes || ''
      })

      // Convert skills object to array
      const skillsArray = Object.entries(teamMember.skills || {}).map(([skillId, skill]) => ({
        id: skillId,
        level: skill.level,
        certificationNumber: skill.certificationNumber,
        expiresAt: skill.expiresAt ? new Date(skill.expiresAt.seconds * 1000).toISOString().split('T')[0] : undefined
      }))
      setSkills(skillsArray)
    } else {
      // Reset form for new member
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        employeeId: '',
        position: '',
        department: '',
        hireDate: '',
        status: 'active',
        availabilityStatus: 'available',
        currentLocation: '',
        shiftStart: '',
        shiftEnd: '',
        emergencyContactName: '',
        emergencyContactPhone: '',
        emergencyContactRelationship: '',
        notes: ''
      })
      setSkills([])
    }
  }, [teamMember])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const addSkill = () => {
    if (newSkillName.trim()) {
      const newSkill: SkillEntry = {
        id: newSkillName.toLowerCase().replace(/\s+/g, '-'),
        level: 'beginner'
      }
      setSkills(prev => [...prev, newSkill])
      setNewSkillName('')
    }
  }

  const updateSkill = (index: number, field: keyof SkillEntry, value: string) => {
    setSkills(prev => prev.map((skill, i) => 
      i === index ? { ...skill, [field]: value } : skill
    ))
  }

  const removeSkill = (index: number) => {
    setSkills(prev => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Convert skills array back to object
    const skillsObject = skills.reduce((acc, skill) => {
      acc[skill.id] = {
        level: skill.level,
        ...(skill.certificationNumber && { certificationNumber: skill.certificationNumber }),
        ...(skill.expiresAt && { expiresAt: skill.expiresAt })
      }
      return acc
    }, {} as Record<string, any>)

    const submitData: Partial<TeamMember> = {
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: formData.email || undefined,
      phone: formData.phone || undefined,
      employeeId: formData.employeeId || undefined,
      position: formData.position,
      department: formData.department,
      hireDate: formData.hireDate,
      status: formData.status,
      skills: skillsObject,
      availability: {
        status: formData.availabilityStatus,
        ...(formData.currentLocation && {
          currentLocation: { name: formData.currentLocation }
        }),
        ...(formData.shiftStart && formData.shiftEnd && {
          shift: {
            start: formData.shiftStart,
            end: formData.shiftEnd
          }
        })
      },
      ...(formData.emergencyContactName && {
        emergencyContact: {
          name: formData.emergencyContactName,
          phone: formData.emergencyContactPhone,
          relationship: formData.emergencyContactRelationship
        }
      }),
      ...(formData.notes && {
        profile: {
          notes: formData.notes
        }
      })
    }

    await onSubmit(submitData)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {teamMember ? 'Edit Team Member' : 'Add New Team Member'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Employment Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Employment Details</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="employeeId">Employee ID</Label>
                <Input
                  id="employeeId"
                  value={formData.employeeId}
                  onChange={(e) => handleInputChange('employeeId', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="hireDate">Hire Date</Label>
                <Input
                  id="hireDate"
                  type="date"
                  value={formData.hireDate}
                  onChange={(e) => handleInputChange('hireDate', e.target.value)}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="position">Position *</Label>
                <Input
                  id="position"
                  value={formData.position}
                  onChange={(e) => handleInputChange('position', e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="department">Department *</Label>
                <Select value={formData.department} onValueChange={(value) => handleInputChange('department', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Operations">Operations</SelectItem>
                    <SelectItem value="Maintenance">Maintenance</SelectItem>
                    <SelectItem value="Safety">Safety</SelectItem>
                    <SelectItem value="Administration">Administration</SelectItem>
                    <SelectItem value="Management">Management</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="on-leave">On Leave</SelectItem>
                    <SelectItem value="terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="availabilityStatus">Availability</Label>
                <Select value={formData.availabilityStatus} onValueChange={(value) => handleInputChange('availabilityStatus', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="available">Available</SelectItem>
                    <SelectItem value="assigned">Assigned</SelectItem>
                    <SelectItem value="busy">Busy</SelectItem>
                    <SelectItem value="off-duty">Off Duty</SelectItem>
                    <SelectItem value="unavailable">Unavailable</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : teamMember ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
